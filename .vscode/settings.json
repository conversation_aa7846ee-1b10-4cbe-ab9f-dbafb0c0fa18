{"editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit", "source.organizeImports": "always"}, "editor.defaultFormatter": "esbenp.prettier-vscode", "[typescript,typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.formatOnSave": true, "eslint.rules.customizations": [{"rule": "*", "severity": "warn"}], "eslint.runtime": "node", "eslint.workingDirectories": [{"pattern": "apps/*/"}, {"pattern": "packages/*/"}, {"pattern": "tooling/*/"}], "prettier.ignorePath": ".giti<PERSON>re", "tailwindCSS.experimental.classRegex": [["cva\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"], ["cx\\(([^)]*)\\)", "(?:'|\"|`)([^']*)(?:'|\"|`)"]], "tailwindCSS.experimental.configFile": "./tooling/tailwind/web.ts", "typescript.enablePromptUseWorkspaceTsdk": true, "typescript.preferences.autoImportFileExcludePatterns": ["next/router.d.ts", "next/dist/client/router.d.ts"], "typescript.tsdk": "node_modules/typescript/lib"}