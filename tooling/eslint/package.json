{"name": "@acme/eslint-config", "private": true, "version": "0.3.0", "type": "module", "exports": {"./base": "./base.js", "./nextjs": "./nextjs.js", "./react": "./react.js"}, "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@eslint/compat": "catalog:", "@next/eslint-plugin-next": "catalog:", "eslint-plugin-import": "catalog:", "eslint-plugin-jsx-a11y": "catalog:", "eslint-plugin-react": "catalog:", "eslint-plugin-react-hooks": "catalog:", "eslint-plugin-turbo": "catalog:", "typescript-eslint": "catalog:"}, "devDependencies": {"@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}