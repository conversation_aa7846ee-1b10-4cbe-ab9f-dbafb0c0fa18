{"name": "@acme/tailwind-config", "version": "0.1.0", "private": true, "type": "module", "exports": {"./web": "./web.ts"}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit"}, "dependencies": {"postcss": "catalog:", "tailwindcss": "catalog:", "tailwindcss-animate": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@tailwindcss/typography": "catalog:", "autoprefixer": "catalog:", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}