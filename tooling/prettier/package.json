{"name": "@acme/prettier-config", "private": true, "version": "0.1.0", "type": "module", "exports": {".": "./index.js"}, "scripts": {"clean": "git clean -xdf .cache .turbo node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "typecheck": "tsc --noEmit"}, "dependencies": {"@ianvs/prettier-plugin-sort-imports": "catalog:", "prettier": "catalog:", "prettier-plugin-tailwindcss": "catalog:"}, "devDependencies": {"@acme/tsconfig": "workspace:*", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}