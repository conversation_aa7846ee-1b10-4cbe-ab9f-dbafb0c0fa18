{"name": "fluidepe-monorepo", "private": true, "engines": {"node": ">=22.12.0", "pnpm": "^9.15.4"}, "packageManager": "pnpm@9.15.4", "scripts": {"build": "turbo run build", "clean": "git clean -xdf node_modules", "clean:workspaces": "turbo run clean", "db:generate": "turbo -F @acme/db db:generate", "db:push": "turbo -F @acme/db push", "db:studio": "turbo -F @acme/db studio", "dev": "turbo watch dev --continue", "dev:next": "turbo watch dev -F @acme/nextjs...", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "format:fix": "turbo run format --continue -- --write --cache --cache-location .cache/.prettiercache", "lint": "turbo run lint --continue -- --cache --cache-location .cache/.eslintcache", "lint:fix": "turbo run lint --continue -- --fix --cache --cache-location .cache/.eslintcache", "lint:ws": "pnpm dlx sherif@latest", "postinstall": "pnpm lint:ws", "typecheck": "turbo run typecheck", "ui-add": "turbo run ui-add", "vercel-postbuild": "pnpm db:generate"}, "devDependencies": {"@acme/prettier-config": "workspace:*", "@turbo/gen": "catalog:", "prettier": "catalog:", "prisma": "catalog:", "turbo": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}