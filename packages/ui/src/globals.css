@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 180 100% 15%;
    --primary-foreground: 210 40% 98%;

    --secondary: 180 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 180 100% 15%;

    --radius: 0.5rem;

    --animation-duration: 700ms;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --radius: 0.5rem;

    --sidebar-background: 240 5.9% 10%;

    --sidebar-foreground: 240 4.8% 95.9%;

    --sidebar-primary: 224.3 76.3% 48%;

    --sidebar-primary-foreground: 0 0% 100%;

    --sidebar-accent: 240 3.7% 15.9%;

    --sidebar-accent-foreground: 240 4.8% 95.9%;

    --sidebar-border: 240 3.7% 15.9%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  body {
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .animate-on-scroll {
    opacity: 0;
    transform: translateY(20px);
    transition:
      opacity var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94),
      transform var(--animation-duration) cubic-bezier(0.25, 0.46, 0.45, 0.94);
    will-change: opacity, transform;
  }

  .animate-on-scroll[data-animation="fade-up"].visible {
    opacity: 1;
    transform: translateY(0);
  }

  .animate-on-scroll[data-animation="fade-down"].visible {
    opacity: 1;
    transform: translateY(0);
  }

  .animate-on-scroll[data-animation="fade-down"] {
    transform: translateY(-20px);
  }

  .animate-on-scroll[data-animation="fade-left"].visible {
    opacity: 1;
    transform: translateX(0);
  }

  .animate-on-scroll[data-animation="fade-left"] {
    transform: translateX(20px);
  }

  .animate-on-scroll[data-animation="fade-right"].visible {
    opacity: 1;
    transform: translateX(0);
  }

  .animate-on-scroll[data-animation="fade-right"] {
    transform: translateX(-20px);
  }

  .animate-on-scroll[data-animation="zoom-in"].visible {
    opacity: 1;
    transform: scale(1);
  }

  .animate-on-scroll[data-animation="zoom-in"] {
    transform: scale(0.8);
  }

  .animate-on-scroll[data-animation="zoom-out"].visible {
    opacity: 1;
    transform: scale(1);
  }

  .animate-on-scroll[data-animation="zoom-out"] {
    transform: scale(1.2);
  }

  .animate-on-scroll[data-animation="flip"].visible {
    opacity: 1;
    transform: rotateY(0);
  }

  .animate-on-scroll[data-animation="flip"] {
    transform: rotateY(90deg);
  }

  .animate-on-scroll[data-animation="bounce"].visible {
    animation: bounce 0.5s ease 1;
  }

  .animate-on-scroll[data-animation="fade-in"].visible {
    opacity: 1;
    transform: translateY(0);
  }

  @keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
      transform: translateY(0);
    }
    40% {
      transform: translateY(-20px);
    }
    60% {
      transform: translateY(-10px);
    }
  }

  .glass-card {
    @apply border border-white/20 bg-white/70 shadow-lg backdrop-blur-md;
  }

  .feature-card {
    @apply glass-card rounded-xl p-5 transition-all duration-300 hover:-translate-y-1 hover:shadow-xl;
  }

  .btn-primary {
    @apply rounded-lg border-none bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal px-6 py-3 font-medium text-white transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
  }

  .btn-secondary {
    @apply rounded-lg border border-fluidpe-teal/30 bg-white px-6 py-3 font-medium text-fluidpe-teal shadow-sm transition-all duration-300 hover:-translate-y-1 hover:border-fluidpe-teal hover:bg-fluidpe-light-teal hover:shadow-md;
  }

  .section-title {
    @apply mb-6 bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal bg-clip-text text-center text-3xl font-bold text-transparent md:text-4xl;
  }

  .section-subtitle {
    @apply mx-auto mb-12 max-w-3xl text-center text-lg text-gray-600 md:text-xl;
  }

  .floating {
    animation: floating 3s ease-in-out infinite;
  }

  @keyframes floating {
    0% {
      transform: translate(0, 0px);
    }
    50% {
      transform: translate(0, 15px);
    }
    100% {
      transform: translate(0, 0px);
    }
  }

  .pulse-slow {
    animation: pulse-slow 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes pulse-slow {
    0%,
    100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(0.95);
    }
  }
}

/* Modern additions */
@layer components {
  .gradient-border {
    position: relative;
    border-radius: 0.5rem;
  }

  .gradient-border::before {
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 0.5rem;
    padding: 2px;
    background: linear-gradient(
      to right,
      theme("colors.fluidpe.teal"),
      theme("colors.fluidpe.medium-teal")
    );
    -webkit-mask:
      linear-gradient(#fff 0 0) content-box,
      linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    pointer-events: none;
  }

  .bg-gradient-text {
    @apply bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal bg-clip-text text-transparent;
  }

  .card-hover-effect {
    @apply transition-all duration-300 hover:-translate-y-1 hover:shadow-lg;
  }

  .shine-effect {
    position: relative;
    overflow: hidden;
  }

  .shine-effect::after {
    content: "";
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(
      to bottom right,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.1) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: rotate(30deg);
    transition: transform 0.5s;
  }

  .shine-effect:hover::after {
    transform: translate(50%, 50%) rotate(30deg);
  }

  .soft-shadow {
    box-shadow: 0 10px 30px -10px rgba(0, 77, 77, 0.1);
  }

  .glass-morphism {
    @apply border border-white/20 bg-white/80 shadow-lg backdrop-blur-lg;
  }

  .gradient-button {
    @apply relative overflow-hidden transition-all duration-300;
  }

  .gradient-button::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.2) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transition: left 0.7s ease-in-out;
  }

  .gradient-button:hover::before {
    left: 100%;
  }

  .elegant-card {
    @apply glass-morphism overflow-hidden rounded-xl transition-all duration-500 hover:shadow-xl;
  }

  .elegant-card:hover {
    transform: translateY(-5px);
  }

  .micro-interaction {
    transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  }

  .micro-interaction:hover {
    transform: scale(1.03);
  }

  .subtle-rotate {
    transition: transform 0.5s ease;
  }

  .subtle-rotate:hover {
    transform: rotate(2deg);
  }

  .glow-on-hover {
    position: relative;
  }

  .glow-on-hover::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: inherit;
    box-shadow: 0 0 0 0 theme("colors.fluidpe.teal");
    opacity: 0;
    transition:
      box-shadow 0.3s ease,
      opacity 0.3s ease;
  }

  .glow-on-hover:hover::after {
    box-shadow: 0 0 15px 3px theme("colors.fluidpe.teal");
    opacity: 0.6;
  }

  /* New enhanced styles */
  .gradient-text-animate {
    background: linear-gradient(
      to right,
      theme("colors.fluidpe.teal"),
      theme("colors.fluidpe.medium-teal"),
      theme("colors.fluidpe.teal")
    );
    background-size: 200% auto;
    background-clip: text;
    text-fill-color: transparent;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: shine 8s ease-in-out infinite;
  }

  .premium-card {
    @apply rounded-2xl border border-white/30 bg-gradient-to-br from-fluidpe-teal/10 via-fluidpe-light-teal/30 to-fluidpe-light-gold/20 shadow-xl backdrop-blur-md transition-all duration-500 hover:border-fluidpe-medium-teal/30 hover:shadow-2xl;
  }

  .savings-highlight {
    @apply relative overflow-hidden rounded-xl border-2 border-green-200 bg-gradient-to-r from-green-50 to-green-100 p-4 font-medium text-green-700;
  }

  .savings-highlight::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      45deg,
      rgba(255, 255, 255, 0) 0%,
      rgba(255, 255, 255, 0.8) 50%,
      rgba(255, 255, 255, 0) 100%
    );
    transform: translateX(-100%);
    animation: shine-sweep 3s infinite;
  }

  @keyframes shine-sweep {
    0% {
      transform: translateX(-100%);
    }
    20%,
    100% {
      transform: translateX(100%);
    }
  }

  .calculator-slider {
    @apply h-2 rounded-full bg-gradient-to-r from-fluidpe-light-teal to-fluidpe-light-gold shadow-inner;
  }

  .calculator-thumb {
    @apply h-6 w-6 rounded-full border-2 border-white bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal shadow-md focus:ring-2 focus:ring-fluidpe-teal;
  }
}

#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}
.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

.card {
  padding: 2em;
}

.read-the-docs {
  color: #888;
}
