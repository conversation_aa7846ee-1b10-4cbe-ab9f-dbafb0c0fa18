import type { Prisma } from "@prisma/client";

export type THTMLHeading = React.DetailedHTMLProps<
  React.HTMLProps<HTMLHeadingElement>,
  HTMLHeadingElement
>;

export type THTMLParagraph = React.DetailedHTMLProps<
  React.HTMLAttributes<HTMLParagraphElement>,
  HTMLParagraphElement
>;

export type THTMLDiv = React.DetailedHTMLProps<
  React.HTMLAttributes<HTMLDivElement>,
  HTMLDivElement
>;

export type THTMLInput = React.DetailedHTMLProps<
  React.HTMLAttributes<HTMLInputElement>,
  HTMLInputElement
>;

// prisma types
export type TBlog = Prisma.BlogGetPayload<{
  select: {
    id: true;
    title: true;
    prevText: true;
    slug: true;
    tags: true;
    createdAt: true;
    updatedAt: true;
    bannerMedia: {
      select: {
        id: true;
        mediaUrl: true;
      };
    };
    blogCategory: {
      select: {
        id: true;
        text: true;
      };
    };
    author: {
      select: {
        id: true;
        name: true;
      };
    };
  };
}>;

export interface TFooterLink {
  link: string;
  title: string;
}

export interface TSocial {
  link: string;
  icon: string;
}

export interface TContactUs {
  email: {
    link: string;
    text: string;
  };
  address: {
    link: string;
    text: string;
  };
  phone_number: {
    link: string;
    text: string;
  };
}

export interface TLegalLink {
  href: string;
  label: string;
}

export interface TFooter {
  logoUrl: string;
  description: string;
  contact_us: {
    title: string;
  } & TContactUs;
  socials: TSocial[];
  about_links: TFooterLink[];
  quick_links: TFooterLink[];
  resources_links: TFooterLink[];
  contactus_links: TFooterLink[];
  meta_description: string;
  cybersecurity_notice: string;
  finTech_digital_lending_complaints: string;
  grievances: string;
  legal_links: TLegalLink[];
  copyright_text: string;
  madeby_text: string;
}

export interface TNavigationLink {
  icon: string;
  text: string;
  link: string;
  subheading?: string;
  links?: TNavigationLink[];
}

export interface NavigationSection {
  title: string;
  links: TNavigationLink[];
}

export interface THeaderNavigationLinks {
  our_offerings?: NavigationSection;
  learn?: NavigationSection;
  resources?: NavigationSection;
  about_us?: NavigationSection;
  browse_categories?: NavigationSection;

  // GUIDE: Add more sections if needed and dont forget to add them in switch case to present in 'desktop-navigation-content.tsx' file to render them.

  // The dropdown content is currently kind of hardcoded not fully hardcoded to accommodate the requirement of rendering multiple, distinct UI components within each section.  A more dynamic approach using an array and conditional mapping was considered but deemed too complex for the initial implementation due to the need to manage diverse UI types.
}

export type TSelectedView = {
  section: string;
  item: string;
  subheading: string;
  links?: TNavigationLink[];
} | null;
