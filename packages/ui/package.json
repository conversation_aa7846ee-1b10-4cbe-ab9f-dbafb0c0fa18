{"name": "@acme/ui", "version": "0.0.0", "private": true, "exports": {"./globals.css": "./src/globals.css", "./lib/*": "./src/lib/*.ts", "./components/*": ["./src/components/*.tsx", "./src/components/*.ts"], "./hooks/*": ["./src/hooks/*.tsx", "./src/hooks/*.ts"]}, "scripts": {"lint": "eslint .", "ui:add": "pnpm dlx shadcn@2.3.0 add"}, "prettier": "@acme/prettier-config", "dependencies": {"@hookform/resolvers": "catalog:", "@radix-ui/react-accordion": "catalog:", "@radix-ui/react-alert-dialog": "catalog:", "@radix-ui/react-aspect-ratio": "catalog:", "@radix-ui/react-avatar": "catalog:", "@radix-ui/react-checkbox": "catalog:", "@radix-ui/react-collapsible": "catalog:", "@radix-ui/react-context-menu": "catalog:", "@radix-ui/react-dialog": "catalog:", "@radix-ui/react-dropdown-menu": "catalog:", "@radix-ui/react-hover-card": "catalog:", "@radix-ui/react-label": "catalog:", "@radix-ui/react-menubar": "catalog:", "@radix-ui/react-navigation-menu": "catalog:", "@radix-ui/react-popover": "catalog:", "@radix-ui/react-progress": "catalog:", "@radix-ui/react-radio-group": "catalog:", "@radix-ui/react-scroll-area": "catalog:", "@radix-ui/react-select": "catalog:", "@radix-ui/react-separator": "catalog:", "@radix-ui/react-slider": "catalog:", "@radix-ui/react-slot": "catalog:", "@radix-ui/react-switch": "catalog:", "@radix-ui/react-tabs": "catalog:", "@radix-ui/react-toast": "catalog:", "@radix-ui/react-toggle": "catalog:", "@radix-ui/react-toggle-group": "catalog:", "@radix-ui/react-tooltip": "catalog:", "class-variance-authority": "catalog:", "clsx": "catalog:", "cmdk": "catalog:", "date-fns": "catalog:", "embla-carousel-react": "catalog:", "input-otp": "catalog:", "lucide-react": "catalog:", "motion": "catalog:", "next-themes": "catalog:", "react": "catalog:react19", "react-day-picker": "^8.10.1", "react-hook-form": "catalog:", "react-resizable-panels": "catalog:", "react-use-measure": "catalog:", "recharts": "catalog:", "sonner": "catalog:", "tailwind-merge": "catalog:", "tailwindcss-animate": "catalog:", "timescape": "catalog:", "vaul": "catalog:", "zod": "^3.24.1"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tailwind-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/react": "catalog:react19", "eslint": "catalog:", "prettier": "catalog:", "react": "catalog:react19", "typescript": "catalog:", "zod": "^3.23.3"}, "peerDependencies": {"react": "catalog:react19", "zod": "^3.23.3"}}