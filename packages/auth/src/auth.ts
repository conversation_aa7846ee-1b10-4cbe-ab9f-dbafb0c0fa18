import type { BetterAuthOptions } from "better-auth";
import { betterAuth } from "better-auth";
import { prismaAdapter } from "better-auth/adapters/prisma";
import { oAuthProxy, phoneNumber } from "better-auth/plugins";

import { prisma as db } from "@acme/db";

import { env } from "../env";
import { account, ID } from "./appwirte";

export const config = {
  database: prismaAdapter(db, {
    provider: "postgresql",
  }),
  secret: env.AUTH_SECRET,
  plugins: [
    oAuthProxy(),
    phoneNumber({
      sendOTP: async ({ code, phoneNumber }) => {
        await account.createPhoneToken(ID.unique(), `+91${phoneNumber}`);
      },
    }),
  ],

  trustedOrigins: ["exp://"],
} satisfies BetterAuthOptions;

export const auth = betterAuth(config);
export type Session = typeof auth.$Infer.Session;
