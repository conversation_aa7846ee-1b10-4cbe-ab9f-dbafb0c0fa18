{"name": "@acme/auth", "version": "0.1.0", "private": true, "license": "MIT", "type": "module", "exports": {".": {"react-server": "./src/index.rsc.ts", "default": "./src/index.ts"}, "./expo": "./src/expo.ts", "./middleware": "./src/middleware.ts", "./client": "./src/client.ts", "./env": "./env.ts"}, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "generate": "pnpx @better-auth/cli generate --output ../db/src/auth-schema.ts", "lint": "eslint", "typecheck": "tsc --noEmit"}, "prettier": "@acme/prettier-config", "dependencies": {"@acme/db": "workspace:*", "@t3-oss/env-nextjs": "catalog:", "appwrite": "^17", "better-auth": "^1.2.7", "next": "catalog:", "react": "catalog:react19", "react-dom": "catalog:react19", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "tsup": "^8.3.5", "typescript": "catalog:"}}