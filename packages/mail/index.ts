import { Resend } from "resend";

import { env } from "./env";

const resend = new Resend(env.RESEND_API_KEY);
interface SendEmailInput {
  to: string;
  subject: string;
  content: React.ReactNode | Promise<React.ReactNode>;
  from?: string;
}
export const sendEmail = async ({
  to,
  subject,
  content,
  from,
}: SendEmailInput) => {
  if (!env.RESEND_API_KEY) {
    throw new Error("RESEND_API_KEY is not defined");
  }

  // If BYPASS_RESEND_OTP is true, don't actually send the email
  if (env.BYPASS_RESEND_OTP) {
    console.info("BYPASS_RESEND_OTP is enabled, skipping actual email sending");
    console.info("Would have sent:", {
      from: from ?? env.RESEND_EMAIL_FROM,
      subject,
      to,
      content,
    });
    return { data: { id: "bypassed-email" }, error: null }; // Return success response
  }

  return resend.emails.send({
    from: from ?? env.RESEND_EMAIL_FROM,
    subject,
    to,
    react: await content,
  });
};
