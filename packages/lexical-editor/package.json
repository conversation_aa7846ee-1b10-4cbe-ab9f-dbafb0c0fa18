{"name": "@acme/lexical-editor", "version": "1.0.0", "description": "", "keywords": [], "license": "ISC", "author": "", "exports": {"./editor": "./App.tsx", "./preview": "./ui/BlogPreview.tsx"}, "dependencies": {"@lexical/headless": "0.25.0", "prettier": "catalog:", "react": "catalog:react19", "react-dom": "catalog:react19", "y-websocket": "^2.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3.3.0", "@excalidraw/excalidraw": "^0.18.0", "@lexical/clipboard": "0.25.0", "@lexical/code": "0.25.0", "@lexical/file": "0.25.0", "@lexical/hashtag": "0.25.0", "@lexical/link": "0.25.0", "@lexical/list": "0.25.0", "@lexical/mark": "0.25.0", "@lexical/overflow": "0.25.0", "@lexical/plain-text": "0.25.0", "@lexical/react": "0.25.0", "@lexical/rich-text": "0.25.0", "@lexical/selection": "0.25.0", "@lexical/table": "0.25.0", "@lexical/utils": "0.25.0", "@types/node": "catalog:", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "eslint": "catalog:", "katex": "^0.16.21", "lexical": "0.25.0", "lodash-es": "^4.17.21", "postcss": "catalog:", "react-error-boundary": "^5.0.0", "tailwindcss": "catalog:", "typescript": "catalog:", "yjs": ">=13.6.24"}}