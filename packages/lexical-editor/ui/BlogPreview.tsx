import { useEffect } from "react";
import { AutoFocusPlugin } from "@lexical/react/LexicalAutoFocusPlugin";
import { CheckListPlugin } from "@lexical/react/LexicalCheckListPlugin";
import { LexicalComposer } from "@lexical/react/LexicalComposer";
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext";
import { ContentEditable } from "@lexical/react/LexicalContentEditable";
import LexicalErrorBoundary from "@lexical/react/LexicalErrorBoundary";
import { HashtagPlugin } from "@lexical/react/LexicalHashtagPlugin";
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin";
import { HorizontalRulePlugin } from "@lexical/react/LexicalHorizontalRulePlugin";
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin";
import { ListPlugin } from "@lexical/react/LexicalListPlugin";
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin";
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin";
import { TabIndentationPlugin } from "@lexical/react/LexicalTabIndentationPlugin";
import { TablePlugin } from "@lexical/react/LexicalTablePlugin";

import PlaygroundNodes from "../nodes/PlaygroundNodes";
import CodeHighlightPlugin from "../plugins/CodeHighlightPlugin";
import CollapsiblePlugin from "../plugins/CollapsiblePlugin";
import EmojisPlugin from "../plugins/EmojisPlugin";
import EquationsPlugin from "../plugins/EquationsPlugin";
import FigmaPlugin from "../plugins/FigmaPlugin";
import ImagesPlugin from "../plugins/ImagesPlugin";
import InlineImagePlugin from "../plugins/InlineImagePlugin";
import KeywordsPlugin from "../plugins/KeywordsPlugin";
import { LayoutPlugin } from "../plugins/LayoutPlugin/LayoutPlugin";
import PageBreakPlugin from "../plugins/PageBreakPlugin";
import PollPlugin from "../plugins/PollPlugin";
import SpecialTextPlugin from "../plugins/SpecialTextPlugin";
import TwitterPlugin from "../plugins/TwitterPlugin";
import YouTubePlugin from "../plugins/YouTubePlugin";
import PlaygroundEditorTheme from "../themes/PlaygroundEditorTheme";

function InitialStatePlugin({ content }: { content: string }) {
  const [editor] = useLexicalComposerContext();

  useEffect(() => {
    if (content) {
      try {
        const parsedContent = JSON.parse(content);
        editor.setEditorState(editor.parseEditorState(parsedContent));
      } catch (error) {
        console.error("Error setting editor state:", error);
      }
    }
  }, [content, editor]);

  return null;
}

type BlogPreviewProps = {
  content: string;
};

export default function BlogPreview({ content }: BlogPreviewProps) {
  const initialConfig = {
    namespace: "BlogPreview",
    nodes: [...PlaygroundNodes],
    theme: PlaygroundEditorTheme,
    editable: false,
    onError: (error: Error) => {
      console.error(error);
    },
  };

  return (
    <LexicalComposer initialConfig={initialConfig}>
      <div className="editor-shell">
        <RichTextPlugin
          contentEditable={<ContentEditable className="editor-input" />}
          placeholder={null}
          ErrorBoundary={LexicalErrorBoundary}
        />
        <InitialStatePlugin content={content} />

        <HistoryPlugin />
        <AutoFocusPlugin />
        <ListPlugin />
        <CheckListPlugin />
        <TablePlugin
          hasCellMerge={true}
          hasCellBackgroundColor={true}
          hasHorizontalScroll={true}
        />
        <HashtagPlugin />
        {/* <AutoLinkPlugin /> */}
        <LinkPlugin />
        <HorizontalRulePlugin />
        <TabIndentationPlugin />
        <MarkdownShortcutPlugin />
        <CodeHighlightPlugin />

        <ImagesPlugin />
        <InlineImagePlugin />
        <TwitterPlugin />
        <YouTubePlugin />
        <FigmaPlugin />

        <EquationsPlugin />
        <CollapsiblePlugin />
        <PageBreakPlugin />
        <LayoutPlugin />
        <KeywordsPlugin />
        <EmojisPlugin />
        <SpecialTextPlugin />
        <PollPlugin />
      </div>
    </LexicalComposer>
  );
}
