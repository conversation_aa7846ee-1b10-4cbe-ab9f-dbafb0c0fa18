{"name": "@acme/api", "version": "0.1.0", "private": true, "type": "module", "exports": {".": {"default": "./src/index.ts"}}, "license": "MIT", "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"@acme/auth": "workspace:*", "@acme/db": "workspace:*", "@acme/validators": "workspace:*", "@trpc/server": "catalog:", "superjson": "catalog:", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}