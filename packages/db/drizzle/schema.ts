import { relations, sql } from "drizzle-orm";
import {
  boolean,
  integer,
  jsonb,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uniqueIndex,
  uuid,
} from "drizzle-orm/pg-core";

export const communicationMode = pgEnum("CommunicationMode", [
  "PHONE_CALL",
  "EMAIL",
  "WHATSAPP",
]);
export const contactStatus = pgEnum("ContactStatus", [
  "PENDING",
  "CONTACTED",
  "SCHEDULED",
  "COMPLETED",
  "NO_RESPONSE",
]);
export const issueType = pgEnum("IssueType", [
  "PAYMENT_ISSUE",
  "ACCOUNT_ISSUE",
  "LOAN_ISSUE",
  "GENERAL_ISSUE",
  "OTHER",
]);
export const referralSource = pgEnum("ReferralSource", [
  "FRIEND_OR_FAMILY",
  "SOCIAL_MEDIA",
  "GOOGLE_SEARCH",
  "EVENT_OR_WEBINAR",
  "OTHER",
]);

export const session = pgTable(
  "Session",
  {
    id: uuid().notNull().primaryKey().defaultRandom(),
    adminId: uuid().notNull(),
    token: text().notNull(),
    expiresAt: timestamp({ precision: 3, mode: "date" }).notNull(),
    ipAddress: text(),
    userAgent: text(),
    createdAt: timestamp({ precision: 3, mode: "date" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp({ precision: 3, mode: "date" })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
  },
  (table) => [
    uniqueIndex("Session_token_key").using(
      "btree",
      table.token.asc().nullsLast(),
    ),
  ],
);

export const sessionRelations = relations(session, ({ one }) => ({
  admin: one(admin, { fields: [session.adminId], references: [admin.id] }),
}));

export const mailSubscription = pgTable(
  "MailSubscription",
  {
    id: uuid().notNull().primaryKey().defaultRandom(),
    createdAt: timestamp({ precision: 3, mode: "string" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp({ precision: 3, mode: "string" })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    customerEmail: text(),
  },
  (table) => [
    uniqueIndex("MailSubscription_customerEmail_key").using(
      "btree",
      table.customerEmail.asc().nullsLast().op("text_ops"),
    ),
  ],
);

export const mailSubscriptionRelations = relations(
  mailSubscription,
  ({ one }) => ({
    customer: one(customer, {
      fields: [mailSubscription.customerEmail],
      references: [customer.email],
    }),
  }),
);

export const customerSupportMessage = pgTable(
  "CustomerSupportMessage",
  {
    id: uuid().notNull().primaryKey().defaultRandom(),
    createdAt: timestamp({ precision: 3, mode: "string" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    message: text().notNull(),
    fullName: text().notNull(),
    email: text().notNull(),
    phone: text().notNull(),
    issueType: issueType().notNull(),
    updatedAt: timestamp({ precision: 3, mode: "string" })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    customerId: uuid().notNull(),
  },
  (table) => [
    uniqueIndex("CustomerSupportMessage_customerId_key").using(
      "btree",
      table.customerId.asc().nullsLast(),
    ),
  ],
);

export const customerSupportMessageRelations = relations(
  customerSupportMessage,
  ({ one }) => ({
    customer: one(customer, {
      fields: [customerSupportMessage.customerId],
      references: [customer.id],
    }),
  }),
);

export const account = pgTable(
  "Account",
  {
    id: uuid().notNull().primaryKey().defaultRandom(),
    adminId: uuid().notNull(),
    accountId: text().notNull(),
    providerId: text().notNull(),
    accessToken: text(),
    refreshToken: text(),
    accessTokenExpiresAt: timestamp({ precision: 3, mode: "date" }),
    refreshTokenExpiresAt: timestamp({ precision: 3, mode: "date" }),
    scope: text(),
    idToken: text(),
    password: text(),
    createdAt: timestamp({ precision: 3, mode: "date" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp({ precision: 3, mode: "date" })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
  },
  (table) => [
    uniqueIndex("Account_providerId_accountId_key").using(
      "btree",
      table.providerId.asc().nullsLast().op("text_ops"),
      table.accountId.asc().nullsLast().op("text_ops"),
    ),
  ],
);

export const accountRelations = relations(account, ({ one }) => ({
  admin: one(admin, { fields: [account.adminId], references: [admin.id] }),
}));

export const customer = pgTable(
  "Customer",
  {
    id: uuid().notNull().primaryKey().defaultRandom(),
    appwriteId: text().notNull(),
    name: text(),
    phone: text(),
    dateOfBirth: text(),
    panNumber: text(),
    panVerifiedResult: jsonb(),
    lastOtpSentAt: timestamp({ precision: 3, mode: "string" }),
    createdAt: timestamp({ precision: 3, mode: "string" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp({ precision: 3, mode: "string" })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
    isPhoneVerified: boolean().default(false).notNull(),
    email: text(),
    finsireId: text(),
    finsireResponse: jsonb(),
    dsaUniqueId: text(),
  },
  (table) => [
    uniqueIndex("Customer_appwriteId_key").using(
      "btree",
      table.appwriteId.asc().nullsLast().op("text_ops"),
    ),
    uniqueIndex("Customer_phone_key").using(
      "btree",
      table.phone.asc().nullsLast().op("text_ops"),
    ),
  ],
);

export const customerRelations = relations(customer, ({ many }) => ({
  supportMessages: many(customerSupportMessage),
  mailSubscriptions: many(mailSubscription),
}));

export const verification = pgTable(
  "Verification",
  {
    id: uuid().notNull().primaryKey().defaultRandom(),
    identifier: text().notNull(),
    value: text().notNull(),
    expiresAt: timestamp({ precision: 3, mode: "string" }).notNull(),
    createdAt: timestamp({ precision: 3, mode: "string" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp({ precision: 3, mode: "string" })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
  },
  (table) => [
    uniqueIndex("Verification_identifier_value_key").using(
      "btree",
      table.identifier.asc().nullsLast().op("text_ops"),
      table.value.asc().nullsLast().op("text_ops"),
    ),
  ],
);

export const blog = pgTable(
  "Blog",
  {
    id: uuid().notNull().primaryKey().defaultRandom(),
    title: text().notNull(),
    slug: text().notNull(),
    excerpt: text().notNull(),
    content: text().notNull(),
    imageUrl: text().notNull(),
    authorName: text().notNull(),
    tags: text().array(),
    published: boolean().default(false).notNull(),
    createdAt: timestamp({ precision: 3, mode: "string" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp({ precision: 3, mode: "string" })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
  },
  (table) => [
    uniqueIndex("Blog_slug_key").using(
      "btree",
      table.slug.asc().nullsLast().op("text_ops"),
    ),
  ],
);

export const faq = pgTable("Faq", {
  id: uuid().notNull().primaryKey().defaultRandom(),
  question: text().notNull(),
  answer: text().notNull(),
  order: integer().default(0).notNull(),
  createdAt: timestamp({ precision: 3, mode: "string" })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: timestamp({ precision: 3, mode: "string" })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
});

export const admin = pgTable(
  "Admin",
  {
    id: uuid().notNull().primaryKey().defaultRandom(),
    name: text().notNull(),
    email: text().notNull(),
    emailVerified: boolean().default(false).notNull(),
    image: text(),
    createdAt: timestamp({ precision: 3, mode: "string" })
      .default(sql`CURRENT_TIMESTAMP`)
      .notNull(),
    updatedAt: timestamp({ precision: 3, mode: "string" })
      .notNull()
      .default(sql`CURRENT_TIMESTAMP`),
  },
  (table) => [
    uniqueIndex("Admin_email_key").using(
      "btree",
      table.email.asc().nullsLast().op("text_ops"),
    ),
  ],
);

export const adminRelations = relations(admin, ({ many }) => ({
  sessions: many(session),
  accounts: many(account),
}));

export const testimonial = pgTable("Testimonial", {
  id: uuid().notNull().primaryKey().defaultRandom(),
  name: text().notNull(),
  designation: text().notNull(),
  location: text().notNull(),
  imageUrl: text().notNull(),
  content: text().notNull(),
  createdAt: timestamp({ precision: 3, mode: "string" })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: timestamp({ precision: 3, mode: "string" })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
});

export const contactRequest = pgTable("ContactRequest", {
  id: uuid().notNull().primaryKey().defaultRandom(),
  name: text().notNull(),
  phone: text().notNull(),
  email: text().notNull(),
  preferredCommunication: communicationMode().notNull(),
  preferredDate: timestamp({ precision: 3, mode: "string" }).notNull(),
  preferredTimeSlot: text().notNull(),
  city: text().notNull(),
  state: text().notNull(),
  referralSource: referralSource().notNull(),
  otherReferralDetails: text(),
  status: contactStatus().default("PENDING").notNull(),
  createdAt: timestamp({ precision: 3, mode: "string" })
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: timestamp({ precision: 3, mode: "string" })
    .notNull()
    .default(sql`CURRENT_TIMESTAMP`),
});
