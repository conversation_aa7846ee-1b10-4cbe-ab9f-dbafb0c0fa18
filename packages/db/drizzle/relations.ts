import { relations } from "drizzle-orm/relations";
import { admin, session, customer, mailSubscription, customerSupportMessage, account } from "./schema";

export const sessionRelations = relations(session, ({one}) => ({
	admin: one(admin, {
		fields: [session.adminId],
		references: [admin.id]
	}),
}));

export const adminRelations = relations(admin, ({many}) => ({
	sessions: many(session),
	accounts: many(account),
}));

export const mailSubscriptionRelations = relations(mailSubscription, ({one}) => ({
	customer: one(customer, {
		fields: [mailSubscription.customerEmail],
		references: [customer.email]
	}),
}));

export const customerRelations = relations(customer, ({many}) => ({
	mailSubscriptions: many(mailSubscription),
	customerSupportMessages: many(customerSupportMessage),
}));

export const customerSupportMessageRelations = relations(customerSupportMessage, ({one}) => ({
	customer: one(customer, {
		fields: [customerSupportMessage.customerId],
		references: [customer.id]
	}),
}));

export const accountRelations = relations(account, ({one}) => ({
	admin: one(admin, {
		fields: [account.adminId],
		references: [admin.id]
	}),
}));