CREATE TYPE "public"."CommunicationMode" AS ENUM('PHONE_CALL', 'EMAIL', 'WHATSAPP');--> statement-breakpoint
CREATE TYPE "public"."ContactStatus" AS ENUM('PENDING', 'CONTACTED', 'SCHEDULED', 'COMPLETED', 'NO_RESPONSE');--> statement-breakpoint
CREATE TYPE "public"."IssueType" AS ENUM('PAYMENT_ISSUE', 'ACCOUNT_ISSUE', 'LOAN_ISSUE', 'GENERAL_ISSUE', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."ReferralSource" AS ENUM('FRIEND_OR_FAMILY', 'SOCIAL_MEDIA', 'GOOGLE_SEARCH', 'EVENT_OR_WEBINAR', 'OTHER');--> statement-breakpoint
CREATE TABLE "Account" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"adminId" uuid NOT NULL,
	"accountId" text NOT NULL,
	"providerId" text NOT NULL,
	"accessToken" text,
	"refreshToken" text,
	"accessTokenExpiresAt" timestamp (3),
	"refreshTokenExpiresAt" timestamp (3),
	"scope" text,
	"idToken" text,
	"password" text,
	"createdAt" timestamp (3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp (3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Admin" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"email" text NOT NULL,
	"emailVerified" boolean DEFAULT false NOT NULL,
	"image" text,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Blog" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"title" text NOT NULL,
	"slug" text NOT NULL,
	"excerpt" text NOT NULL,
	"content" text NOT NULL,
	"imageUrl" text NOT NULL,
	"authorName" text NOT NULL,
	"tags" text[],
	"published" boolean DEFAULT false NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "ContactRequest" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"phone" text NOT NULL,
	"email" text NOT NULL,
	"preferredCommunication" "CommunicationMode" NOT NULL,
	"preferredDate" timestamp(3) NOT NULL,
	"preferredTimeSlot" text NOT NULL,
	"city" text NOT NULL,
	"state" text NOT NULL,
	"referralSource" "ReferralSource" NOT NULL,
	"otherReferralDetails" text,
	"status" "ContactStatus" DEFAULT 'PENDING' NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Customer" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"appwriteId" text NOT NULL,
	"name" text,
	"phone" text,
	"dateOfBirth" text,
	"panNumber" text,
	"panVerifiedResult" jsonb,
	"lastOtpSentAt" timestamp(3),
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"isPhoneVerified" boolean DEFAULT false NOT NULL,
	"email" text
);
--> statement-breakpoint
CREATE TABLE "CustomerSupportMessage" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"message" text NOT NULL,
	"fullName" text NOT NULL,
	"email" text NOT NULL,
	"phone" text NOT NULL,
	"issueType" "IssueType" NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"customerId" uuid NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Faq" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"question" text NOT NULL,
	"answer" text NOT NULL,
	"order" integer DEFAULT 0 NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "MailSubscription" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"customerEmail" text
);
--> statement-breakpoint
CREATE TABLE "Session" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"adminId" uuid NOT NULL,
	"token" text NOT NULL,
	"expiresAt" timestamp (3) NOT NULL,
	"ipAddress" text,
	"userAgent" text,
	"createdAt" timestamp (3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp (3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Testimonial" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" text NOT NULL,
	"designation" text NOT NULL,
	"location" text NOT NULL,
	"imageUrl" text NOT NULL,
	"content" text NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "Verification" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"identifier" text NOT NULL,
	"value" text NOT NULL,
	"expiresAt" timestamp(3) NOT NULL,
	"createdAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updatedAt" timestamp(3) DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX "Account_providerId_accountId_key" ON "Account" USING btree ("providerId" text_ops,"accountId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Admin_email_key" ON "Admin" USING btree ("email" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Blog_slug_key" ON "Blog" USING btree ("slug" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Customer_appwriteId_key" ON "Customer" USING btree ("appwriteId" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Customer_phone_key" ON "Customer" USING btree ("phone" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "CustomerSupportMessage_customerId_key" ON "CustomerSupportMessage" USING btree ("customerId");--> statement-breakpoint
CREATE UNIQUE INDEX "MailSubscription_customerEmail_key" ON "MailSubscription" USING btree ("customerEmail" text_ops);--> statement-breakpoint
CREATE UNIQUE INDEX "Session_token_key" ON "Session" USING btree ("token");--> statement-breakpoint
CREATE UNIQUE INDEX "Verification_identifier_value_key" ON "Verification" USING btree ("identifier" text_ops,"value" text_ops);