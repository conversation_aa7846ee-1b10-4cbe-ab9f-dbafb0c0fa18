import bcrypt from "bcryptjs";

import {
  account as accountTable,
  admin as adminTable,
} from "../../drizzle/schema";
import { db } from "../client";
import { eq } from "../index";

const password = "fluidpe@123#_admin";

async function main() {
  try {
    const existingAdmin = await db.query.admin.findFirst({
      where: eq(adminTable.email, "<EMAIL>"),
    });

    if (existingAdmin) {
      console.log("Default admin already exists, skipping seed.");
      return;
    }

    const [adminRes] = await db
      .insert(adminTable)
      .values({
        name: "Admin User",
        email: "<EMAIL>",
        emailVerified: true,
        image: null,
      })
      .returning();

    const hashedPassword = await bcrypt.hash(password, 10);
    await db.insert(accountTable).values({
      adminId: adminRes?.id!,
      accountId: adminRes?.id!,
      providerId: "credential",
      password: hashedPassword,
    });

    console.log(`Created default admin with id: ${adminRes?.id}`);
  } catch (error) {
    console.error("Error seeding default admin:", error);
    throw error;
  } finally {
    console.log("Seeding completed");
    process.exit(0);
  }
}

main()
  .then(async () => {
    process.exit(0);
  })
  .catch(async (e) => {
    console.error(e);
    process.exit(1);
  });
