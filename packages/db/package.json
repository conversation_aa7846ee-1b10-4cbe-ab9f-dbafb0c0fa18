{"name": "@acme/db", "version": "0.1.0", "private": true, "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./client": {"default": "./src/client.ts"}, "./schema": {"default": "./drizzle/schema.ts"}}, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "seed": "pnpm with-env pnpx tsx src/seed/admin.ts", "push": "pnpm with-env drizzle-kit  push", "generate": "pnpm with-env drizzle-kit generate", "migrate": "pnpm with-env drizzle-kit migrate", "pull": "pnpm with-env drizzle-kit pull", "studio": "pnpm with-env drizzle-kit studio", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "with-env": "dotenv -e ../../.env --"}, "dependencies": {"@neondatabase/serverless": "^1.0.0", "@t3-oss/env-core": "catalog:", "drizzle-orm": "^0.43.1", "drizzle-seed": "^0.3.1", "drizzle-zod": "^0.7.1", "postgres": "^3.4.5", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/pg": "^8.11.14", "bcryptjs": "catalog:", "dotenv-cli": "catalog:", "drizzle-kit": "^0.31.0", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}