{"name": "@acme/validators", "private": true, "version": "0.1.0", "type": "module", "exports": {"./admin": {"types": "./dist/admin-schema.d.ts", "default": "./src/admin-schema.ts"}, "./customer": {"types": "./dist/customer-schema.d.ts", "default": "./src/customer-schema.ts"}, "./fluidpe": {"types": "./dist/fluidpe-schema.d.ts", "default": "./src/fluidpe-schema.ts"}}, "license": "MIT", "scripts": {"build": "tsc", "clean": "git clean -xdf .cache .turbo dist node_modules", "dev": "tsc", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "dependencies": {"zod": "catalog:"}, "devDependencies": {"@acme/db": "workspace:*", "@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tsconfig": "workspace:*", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@acme/prettier-config"}