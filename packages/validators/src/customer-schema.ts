import { z } from "zod";

export const LoginFormSchema = z.object({
  phoneNumber: z.string().length(10, {
    message: "Phone number must be 10 digits long",
  }),
});

export const OTPVerificationFormSchema = z.object({
  otp: z.string().min(6, {
    message: "Your one-time password must be 6 characters.",
  }),
});

export const OnboardingFormSchema = z.object({
  fullName: z.string().min(2, {
    message: "Full name must be at least 2 characters.",
  }),
  dateOfBirth: z.string().refine(
    (date) => {
      const parsedDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - parsedDate.getFullYear();
      return !isNaN(parsedDate.getTime()) && age >= 18;
    },
    {
      message: "You must be at least 18 years old.",
    },
  ),
  panCard: z
    .string()
    .min(10, {
      message: "PAN card must be 10 characters long",
    })
    .max(10, {
      message: "PAN card must be 10 characters long",
    }),
});

export const LoanApplicationFormSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  phone: z.string().length(10, {
    message: "Phone number must be 10 digits long",
  }),
  pan: z
    .string()
    .min(10, {
      message: "PAN must be 10 characters long",
    })
    .max(10, {
      message: "PAN must be 10 characters long",
    })
    .regex(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, {
      message: "Please enter a valid PAN number",
    }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  // Empty string also allowed for dsaUniqueId
  dsaUniqueId: z
    .string()
    .length(4, {
      message: "DSA Unique ID must be 4 digits long",
    })
    .optional()
    .nullable(),
  latitude: z.string().min(1, {
    message: "Latitude is required",
  }),
  longitude: z.string().min(1, {
    message: "Longitude is required",
  }),
});
