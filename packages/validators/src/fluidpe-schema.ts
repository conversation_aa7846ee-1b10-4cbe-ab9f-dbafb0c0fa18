import { z } from "zod";

import { communicationMode, referralSource } from "@acme/db/schema";

export const TalkToAdvisorSchema = z.object({
  name: z.string().min(2, {
    message: "Name must be at least 2 characters.",
  }),
  phone: z.string().min(10, {
    message: "Please enter a valid phone number.",
  }),
  email: z.string().email({
    message: "Please enter a valid email address.",
  }),
  preferredCommunication: z.enum(communicationMode.enumValues),
  preferredDate: z.date(),
  preferredTimeSlot: z.string(),
  city: z.string().min(2, {
    message: "City must be at least 2 characters.",
  }),
  state: z.string().min(2, {
    message: "State must be at least 2 characters.",
  }),
  referralSource: z.enum(referralSource.enumValues),
  otherReferralDetails: z.string().optional(),
});
