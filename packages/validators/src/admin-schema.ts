import { z } from "zod";

export const AdminLoginSchema = z.object({
  email: z.string().email({ message: "Please enter a valid email address." }),
  password: z
    .string()
    .min(8, { message: "Password must be at least 8 characters long." }),
});

export const FaqCRUDSchema = z.object({
  question: z
    .string()
    .min(5, { message: "Question must be at least 5 characters" }),
  answer: z
    .string()
    .min(10, { message: "Answer must be at least 10 characters" }),
});

export const BlogCRUDSchema = z.object({
  title: z
    .string()
    .min(5, "Title must be at least 5 characters")
    .max(100, "Title must be at most 100 characters"),
  excerpt: z
    .string()
    .min(10, "Excerpt must be at least 10 characters")
    .max(300, "Excerpt must be at most 300 characters"),
  content: z.string().min(50, "Content must be at least 50 characters"),
  imageUrl: z.string().url("Please enter a valid URL"),
  authorName: z.string().min(2, "Author name is required"),
  tags: z.array(z.string()),
  published: z.boolean().default(false),
});

export const TestimonialCRUDSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters" }),
  designation: z
    .string()
    .min(2, { message: "Designation must be at least 2 characters" }),
  location: z
    .string()
    .min(2, { message: "Location must be at least 2 characters" }),
  imageUrl: z.string().url({ message: "Please enter a valid image URL" }),
  content: z
    .string()
    .min(10, { message: "Content must be at least 10 characters" }),
});
