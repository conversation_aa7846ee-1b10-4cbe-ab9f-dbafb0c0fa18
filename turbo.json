{"$schema": "https://turborepo.org/schema.json", "ui": "tui", "tasks": {"topo": {"dependsOn": ["^topo"]}, "build": {"dependsOn": ["^build"], "outputs": [".cache/tsbuildinfo.json", "dist/**"], "env": ["DATABASE_URL", "RESEND_API_KEY", "RESEND_EMAIL_FROM", "UPLOADTHING_TOKEN", "BYPASS_RESEND_OTP"]}, "dev": {"dependsOn": ["^dev"], "cache": false, "persistent": false}, "format": {"outputs": [".cache/.prettiercache"], "outputLogs": "new-only"}, "lint": {"dependsOn": ["^topo", "^build"], "outputs": [".cache/.eslintcache"]}, "typecheck": {"dependsOn": ["^topo", "^build"], "outputs": [".cache/tsbuildinfo.json"]}, "clean": {"cache": false}, "//#clean": {"cache": false}, "push": {"cache": false, "interactive": true}, "studio": {"cache": false, "persistent": true}, "ui-add": {"cache": false, "interactive": true}}, "globalEnv": ["DATABASE_URL", "AUTH_REDIRECT_PROXY_URL", "PORT"], "globalPassThroughEnv": ["NODE_ENV", "CI", "VERCEL", "VERCEL_ENV", "VERCEL_URL", "npm_lifecycle_event"]}