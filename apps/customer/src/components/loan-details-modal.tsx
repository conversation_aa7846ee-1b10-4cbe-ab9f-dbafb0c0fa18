"use client";

import { CreditCard, Calendar, DollarSign, FileText, CheckCircle, Clock, XCircle } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@acme/ui/components/ui/dialog";
import { Badge } from "@acme/ui/components/ui/badge";
import { Separator } from "@acme/ui/components/ui/separator";

interface LoanDetailsModalProps {
  loan: any;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const LoanDetailsModal = ({ loan, open, onOpenChange }: LoanDetailsModalProps) => {
  if (!loan) return null;

  const getStatusIcon = (type: string) => {
    switch (type) {
      case "active":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "inprogress":
        return <Clock className="h-5 w-5 text-orange-600" />;
      case "closed":
        return <XCircle className="h-5 w-5 text-gray-600" />;
      default:
        return <FileText className="h-5 w-5" />;
    }
  };

  const getStatusColor = (type: string) => {
    switch (type) {
      case "active":
        return "bg-green-100 text-green-800 border-green-200";
      case "inprogress":
        return "bg-orange-100 text-orange-800 border-orange-200";
      case "closed":
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-blue-100 text-blue-800 border-blue-200";
    }
  };

  const getStatusText = (type: string) => {
    switch (type) {
      case "active":
        return "Active Loan";
      case "inprogress":
        return "In Progress";
      case "closed":
        return "Closed Loan";
      default:
        return "Unknown";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5 text-fluidpe-teal" />
            Loan Details
          </DialogTitle>
          <DialogDescription>
            Detailed information about your loan application
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Status Badge */}
          <div className="flex justify-center">
            <Badge
              variant="outline"
              className={`flex items-center gap-2 px-4 py-2 text-sm font-medium ${getStatusColor(loan.type)}`}
            >
              {getStatusIcon(loan.type)}
              {getStatusText(loan.type)}
            </Badge>
          </div>

          <Separator />

          {/* Loan Information */}
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600">Loan ID:</span>
              <span className="text-sm font-mono">{loan.loan_id}</span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-600">LAN:</span>
              <span className="text-sm font-mono">{loan.lan}</span>
            </div>

            {loan.loan_amount && (
              <div className="flex items-center justify-between">
                <span className="flex items-center gap-1 text-sm font-medium text-gray-600">
                  <DollarSign className="h-4 w-4" />
                  Amount:
                </span>
                <span className="text-lg font-semibold text-green-600">
                  ₹{loan.loan_amount.toLocaleString()}
                </span>
              </div>
            )}

            {loan.current_status && (
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-600">Current Status:</span>
                <Badge variant="secondary" className="text-xs">
                  {loan.current_status}
                </Badge>
              </div>
            )}

            {loan.created_at && (
              <div className="flex items-center justify-between">
                <span className="flex items-center gap-1 text-sm font-medium text-gray-600">
                  <Calendar className="h-4 w-4" />
                  Applied On:
                </span>
                <span className="text-sm">{loan.created_at}</span>
              </div>
            )}
          </div>

          <Separator />

          {/* Additional Information */}
          <div className="rounded-lg bg-gray-50 p-3">
            <h4 className="mb-2 text-sm font-medium text-gray-700">Information</h4>
            <div className="space-y-1 text-xs text-gray-600">
              {loan.type === "active" && (
                <p>• Your loan is currently active and funds are available</p>
              )}
              {loan.type === "inprogress" && (
                <>
                  <p>• Your loan application is being processed</p>
                  <p>• Current status: {loan.current_status}</p>
                  <p>• You will be notified once approved</p>
                </>
              )}
              {loan.type === "closed" && (
                <p>• This loan has been successfully closed</p>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LoanDetailsModal;
