"use client";

import { useState } from "react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { MessageCircleQuestionIcon } from "lucide-react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from "@acme/ui/components/ui/sidebar";

import LoanApplicationModal from "~/components/loan-application-modal";
import { DASHBOARD_SIDEBAR_ITEMS } from "~/lib/constants";
import { NavFooter } from "./sidebar-footer";

// Menu items.
interface User {
  name: string;
  email: string;
  avatar: string;
}

export function AppSidebar({ user }: { user: User }) {
  const currentPath = usePathname();
  const [isLoanModalOpen, setIsLoanModalOpen] = useState(false);

  const handleItemClick = (item: (typeof DASHBOARD_SIDEBAR_ITEMS)[0]) => {
    if (item.type === "modal" && item.action === "loan-application") {
      setIsLoanModalOpen(true);
    }
  };

  return (
    <>
      <Sidebar collapsible="icon" className="">
        <SidebarHeader className="items-center">
          <div className="relative aspect-square w-[44px]">
            <Image src="/static/icons/sidebar-logo.svg" alt="logo" fill></Image>
          </div>
        </SidebarHeader>
        <SidebarSeparator />
        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupContent>
              <SidebarMenu className="gap-6">
                {DASHBOARD_SIDEBAR_ITEMS.map((item) => (
                  <SidebarMenuItem
                    key={item.title}
                    className={`p-3 hover:bg-none ${currentPath === item.url && item.type === "link" && "rounded-lg bg-[#E9F4FF]"}`}
                  >
                    <SidebarMenuButton
                      className="p-0 hover:bg-transparent active:bg-transparent"
                      asChild={item.type === "link"}
                    >
                      {item.type === "link" ? (
                        <Link href={item.url} className="items-center !p-0">
                          <item.icon className="h-7 min-h-7 w-7 min-w-7" />
                          <span className="text-base font-medium text-zinc-900">
                            {item.title}
                          </span>
                        </Link>
                      ) : (
                        <button
                          onClick={() => handleItemClick(item)}
                          className="flex w-full items-center !p-0"
                        >
                          <item.icon className="h-7 min-h-7 w-7 min-w-7" />
                          <span className="text-base font-medium text-zinc-900">
                            {item.title}
                          </span>
                        </button>
                      )}
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>
        <SidebarFooter>
          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupContent>
                <SidebarMenu className="gap-6">
                  <SidebarMenuItem
                    key={"faq"}
                    className={`${currentPath === "/dashboard/faq" && "items-center justify-center rounded-lg bg-[#E9F4FF] p-3"}`}
                  >
                    <SidebarMenuButton
                      className="self-center p-0 hover:bg-transparent active:bg-transparent"
                      asChild
                    >
                      <Link href={"/dashboard/faq"}>
                        <MessageCircleQuestionIcon
                          className="min-h-7 min-w-7"
                          size={28}
                        />
                        <span className="text-base font-medium text-zinc-900">
                          Faq
                        </span>
                      </Link>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>{" "}
          <NavFooter user={user}></NavFooter>
        </SidebarFooter>
      </Sidebar>

      <LoanApplicationModal
        open={isLoanModalOpen}
        onOpenChange={setIsLoanModalOpen}
      />
    </>
  );
}
