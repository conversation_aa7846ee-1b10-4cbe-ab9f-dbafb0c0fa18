"use client";

import type { z } from "zod";
import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { MapPin } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@acme/ui/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { LoanApplicationFormSchema } from "@acme/validators/customer";

import { api } from "~/trpc/react";

interface LoanApplicationModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const LoanApplicationModal = ({
  open,
  onOpenChange,
}: LoanApplicationModalProps) => {
  const [isGettingLocation, setIsGettingLocation] = useState(false);

  // Fetch user profile data to prefill the form
  const { data: profile, isLoading: profileLoading } =
    api.profile.getProfile.useQuery();

  // Form setup
  const form = useForm<z.infer<typeof LoanApplicationFormSchema>>({
    resolver: zodResolver(LoanApplicationFormSchema),
    defaultValues: {
      name: "",
      phone: "",
      pan: "",
      email: "",
      dsaUniqueId: "",
      latitude: "",
      longitude: "",
    },
    mode: "onChange",
  });

  // Prefill form with user data when profile is loaded
  useEffect(() => {
    if (profile) {
      form.setValue("name", profile.name ?? "");
      form.setValue("phone", profile.phone?.replace("+91", "") ?? "");
      form.setValue("pan", profile.panNumber ?? "");
      form.setValue("email", profile.email ?? "");
      form.setValue("dsaUniqueId", profile.dsaUniqueId ?? null);
    }
  }, [profile, form]);

  // Get user's current location
  const getCurrentLocation = () => {
    setIsGettingLocation(true);

    if (!navigator.geolocation) {
      toast.error("Geolocation is not supported by this browser");
      setIsGettingLocation(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        const { latitude, longitude } = position.coords;
        form.setValue("latitude", latitude.toString());
        form.setValue("longitude", longitude.toString());
        toast.success("Location captured successfully");
        setIsGettingLocation(false);
      },
      (error) => {
        let errorMessage = "Failed to get location";
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage =
              "Location access denied by you. We need location access to be able to start your loan application. Please enable location access in your browser settings.";
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = "Location information is unavailable";
            break;
          case error.TIMEOUT:
            errorMessage = "Location request timed out";
            break;
        }
        toast.error(errorMessage);
        setIsGettingLocation(false);
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 60000,
      },
    );
  };

  // Apply loan mutation
  const applyLoan = api.dashboard.applyLoan.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      form.reset();
      onOpenChange(false);
      window.open(data.finsireLink);
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const onSubmit = (values: z.infer<typeof LoanApplicationFormSchema>) => {
    applyLoan.mutate(values);
  };

  // Auto-get location when modal opens
  useEffect(() => {
    if (open && !form.getValues("latitude")) {
      getCurrentLocation();
    }
  }, [open, form]);

  // Reset form when modal closes
  useEffect(() => {
    if (!open) {
      form.reset();
    }
  }, [open, form]);

  const latitude = form.watch("latitude");
  const longitude = form.watch("longitude");

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-fluidpe-teal">
            Apply for Loan
          </DialogTitle>
          <DialogDescription>
            Fill out the form below to apply for your loan. We'll process your
            application quickly and securely.
          </DialogDescription>
        </DialogHeader>

        {profileLoading ? (
          <div className="flex h-32 items-center justify-center">
            <div className="text-lg">Loading...</div>
          </div>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        Full Name *
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="px-3 py-2"
                          placeholder="Enter your full name"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        Phone Number *
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="px-3 py-2"
                          placeholder="Enter your phone number"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="pan"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        PAN Number *
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="px-3 py-2 uppercase"
                          placeholder="**********"
                          {...field}
                          onChange={(e) =>
                            field.onChange(e.target.value.toUpperCase())
                          }
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        Email Address *
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="px-3 py-2"
                          placeholder="Enter your email address"
                          type="email"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="dsaUniqueId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">
                      DSA ID
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="px-3 py-2"
                        placeholder="Enter your 4-digit DSA ID"
                        {...field}
                        maxLength={4}
                        onChange={(e) =>
                          field.onChange(e.target.value ? e.target.value : "")
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <FormField
                  control={form.control}
                  name="latitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        Latitude *
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="px-3 py-2"
                          placeholder="Auto-detected"
                          readOnly
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="longitude"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">
                        Longitude *
                      </FormLabel>
                      <FormControl>
                        <Input
                          className="px-3 py-2"
                          placeholder="Auto-detected"
                          readOnly
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div> */}
              {/* Show error if location not detected */}
              {(!latitude || !longitude) && (
                <div className="text-sm text-red-500">
                  Please enable location access to be able to start your loan
                  application.
                </div>
              )}

              <div className="flex items-center justify-center py-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={getCurrentLocation}
                  disabled={isGettingLocation}
                  className="flex items-center gap-2"
                >
                  <MapPin className="h-4 w-4" />
                  {isGettingLocation
                    ? "Getting Location..."
                    : "Get Current Location"}
                </Button>
              </div>

              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={applyLoan.isPending}
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  disabled={applyLoan.isPending || !form.getValues("latitude")}
                  className="bg-fluidpe-teal hover:bg-fluidpe-teal/90"
                >
                  {applyLoan.isPending ? "Submitting..." : "Apply for Loan"}
                </Button>
              </div>
            </form>
          </Form>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default LoanApplicationModal;
