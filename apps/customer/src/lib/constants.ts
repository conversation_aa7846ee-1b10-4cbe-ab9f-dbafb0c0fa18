import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Chart<PERSON>ie,
  FileText,
  LayoutDashboard,
  Settings2,
  SquareTerminal,
  Users,
} from "lucide-react";

export const AUTH_LAYOUT_CARDS = [
  {
    icon: "/static/icons/customer-auth-layout-icon-1.svg",
    text: "Low Interest Rate",
  },
  {
    icon: "/static/icons/customer-auth-layout-icon-2.svg",
    text: "Secure & Transparent Process",
  },
  {
    icon: "/static/icons/customer-auth-layout-icon-3.svg",
    text: "Fast & Easy Application",
  },
];

export const DASHBOARD_SIDEBAR_ITEMS = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: LayoutDashboard,
    type: "link" as const,
  },
  // {
  //   title: "Apply for Loan",
  //   url: "#",
  //   icon: CreditCard,
  //   type: "modal" as const,
  //   action: "loan-application",
  // },
  {
    title: "My Loan Profile",
    url: "#",
    icon: Chart<PERSON>ie,
    type: "link" as const,
  },
  {
    title: "Profile Details",
    url: "/dashboard/profile",
    icon: Users,
    type: "link" as const,
  },
  {
    title: "Contact Us",
    url: "/dashboard/contact-us",
    icon: FileText,
    type: "link" as const,
  },
];

export const NAV_FOOTER = {
  user: {
    name: "shadcn",
    email: "<EMAIL>",
    avatar: "/avatars/shadcn.jpg",
  },
  navMain: [
    {
      title: "Playground",
      url: "#",
      icon: SquareTerminal,
      isActive: true,
      items: [
        {
          title: "History",
          url: "#",
        },
        {
          title: "Starred",
          url: "#",
        },
        {
          title: "Settings",
          url: "#",
        },
      ],
    },
    {
      title: "Models",
      url: "#",
      icon: Bot,
      items: [
        {
          title: "Genesis",
          url: "#",
        },
        {
          title: "Explorer",
          url: "#",
        },
        {
          title: "Quantum",
          url: "#",
        },
      ],
    },
    {
      title: "Documentation",
      url: "#",
      icon: BookOpen,
      items: [
        {
          title: "Introduction",
          url: "#",
        },
        {
          title: "Get Started",
          url: "#",
        },
        {
          title: "Tutorials",
          url: "#",
        },
        {
          title: "Changelog",
          url: "#",
        },
      ],
    },
    {
      title: "Settings",
      url: "#",
      icon: Settings2,
      items: [
        {
          title: "General",
          url: "#",
        },
        {
          title: "Team",
          url: "#",
        },
        {
          title: "Billing",
          url: "#",
        },
        {
          title: "Limits",
          url: "#",
        },
      ],
    },
  ],
};
