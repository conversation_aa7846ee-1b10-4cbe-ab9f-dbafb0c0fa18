"use server";

import { cookies } from "next/headers";
import { redirect } from "next/navigation";
import { ID } from "node-appwrite";

import { createAdminClient } from "~/server/auth/appwrite";

export async function sendOtp({ phoneNumber }: { phoneNumber: string }) {
  const { account } = await createAdminClient();
  console.log("account", account);

  const token = await account.createPhoneToken(
    ID.unique(),
    `+91${phoneNumber}`,
  );
  console.log("token", token);
  return redirect(
    `/otp-verification?phoneNumber=${phoneNumber}&userId=${token.userId}`,
  );
}

export async function verifyOtp({
  otp,
  userId,
}: {
  otp: string;
  userId: string;
}) {
  const { account } = await createAdminClient();

  const session = await account.createSession(userId, otp);
  (await cookies()).set("appwrite-session", session.secret, {
    path: "/",
    httpOnly: true,
    sameSite: "strict",
    secure: true,
  });
  return redirect("/");
}
