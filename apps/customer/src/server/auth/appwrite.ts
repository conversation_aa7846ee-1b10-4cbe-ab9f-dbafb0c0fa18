"use server";

import { cookies } from "next/headers";
import { Account, Client } from "node-appwrite";

import { env } from "~/env";

export async function createSessionClient() {
  const client = new Client()
    .setEndpoint(env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
    .setProject(env.NEXT_PUBLIC_APPWRITE_PROJECT_ID);

  const session = (await cookies()).get("appwrite-session");
  if (!session?.value) {
    throw new Error("No session");
  }

  client.setSession(session.value);

  return {
    get account() {
      return new Account(client);
    },
  };
}

export async function createAdminClient() {
  console.log(
    "env.NEXT_PUBLIC_APPWRITE_ENDPOINT",
    env.NEXT_PUBLIC_APPWRITE_ENDPOINT,
  );
  console.log(
    "env.NEXT_PUBLIC_APPWRITE_PROJECT_ID",
    env.NEXT_PUBLIC_APPWRITE_PROJECT_ID,
  );
  console.log("env.NEXT_APPWRITE_KEY", env.NEXT_APPWRITE_KEY);
  const client = new Client()
    .setEndpoint(env.NEXT_PUBLIC_APPWRITE_ENDPOINT)
    .setProject(env.NEXT_PUBLIC_APPWRITE_PROJECT_ID)
    .setKey(env.NEXT_APPWRITE_KEY)
    .setSelfSigned(true);

  return {
    get account() {
      return new Account(client);
    },
  };
}
// ... your initilization functions

export async function getLoggedInUser() {
  try {
    const { account } = await createSessionClient();
    return await account.get().then((user) => {
      return {
        phoneVerification: user.phoneVerification,
        phone: user.phone,
        id: user.$id,
        name: user.name,
      };
    });
  } catch (error) {
    console.error(error);
    return null;
  }
}

export async function signOut() {
  const { account } = await createSessionClient();
  await account.deleteSession("current");
}
