import { authRouter } from "./router/auth";
import { dashboardRouter } from "./router/dashboard";
import { profileRouter } from "./router/profile";
import { supportRouter } from "./router/support";
import { createTRPCRouter } from "./trpc";

export const appRouter = createTRPCRouter({
  auth: authRouter,
  support: supportRouter,
  profile: profileRouter,
  dashboard: dashboardRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;
