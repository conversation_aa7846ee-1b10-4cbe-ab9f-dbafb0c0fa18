import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";

import { eq } from "@acme/db";
import { customer } from "@acme/db/schema";
import { LoanApplicationFormSchema } from "@acme/validators/customer";

import { env } from "~/env";
import { protectedProcedure, publicProcedure } from "../trpc";

// Finsire loan data types
interface FinsireLoan {
  loan_id: string;
  lan: string;
  loan_amount?: number;
  current_status?: string;
  created_at?: string;
}

interface FinsireOverdrafts {
  active_overdrafts: FinsireLoan[];
  inprogress_overdrafts: FinsireLoan[];
  closed_overdrafts: FinsireLoan[];
}

interface FinsireLoanResponse {
  status: boolean;
  data: {
    overdrafts: FinsireOverdrafts;
    finsire_id: string;
  };
}

export const dashboardRouter = {
  getAllFaqs: publicProcedure.query(async ({ ctx }) => {
    try {
      const faqs = await ctx.db.query.faq.findMany({});

      return faqs;
    } catch (err) {
      if (err instanceof TRPCError) {
        throw err;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "An error occurred while fetching FAQs",
        cause: err,
      });
    }
  }),

  applyLoan: protectedProcedure
    .input(LoanApplicationFormSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Create authentication token for Finsire API
        const token = `${env.FINSIRE_CLIENT_ID}:${env.FINSIRE_CLIENT_SECRET}`;
        const encryptedToken = Buffer.from(token)
          .toString("base64")
          .replace(/\+/g, "-")
          .replace(/\//g, "_")
          .replace(/=+$/, "");

        console.log("encryptedToken", encryptedToken);

        // Prepare the API payload
        const payload = {
          user: {
            name: input.name,
            phone: input.phone,
            pan: input.pan,
            latitude: input.latitude,
            longitude: input.longitude,
            email: input.email,
            "Aadhar Number": `00000000${input.dsaUniqueId}`,
          },
        };

        console.log("HTTP URL is", `${env.FINSIRE_API_URL}/assets_selection`);

        // Call Finsire API
        const response = await fetch(
          `${env.FINSIRE_API_URL}/assets_selection`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: encryptedToken,
            },
            body: JSON.stringify(payload),
          },
        );

        if (!response.ok) {
          console.log("result is", await response.text());
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Finsire API error: ${response.status} ${response.statusText}`,
          });
        }

        const result = (await response.json()) as {
          link: string;
          finsire_id: string;
          status: boolean;
          message: string;
          summary_link: null;
          widthdrawal_link: null;
        };

        await ctx.db
          .update(customer)
          .set({
            finsireId: result.finsire_id,
            finsireResponse: result,
          })
          .where(eq(customer.id, ctx.session.id!));

        return {
          success: true,
          finsireLink: result.link,
          message: "Loan application submitted successfully",
        };
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "An error occurred while submitting the loan application",
          cause: err,
        });
      }
    }),

  getUserLoans: protectedProcedure.query(async ({ ctx }) => {
    try {
      // Get user's finsire_id from database
      const user = await ctx.db.query.customer.findFirst({
        where: eq(customer.id, ctx.session.id!),
        columns: {
          finsireId: true,
        },
      });

      if (!user?.finsireId) {
        return {
          hasLoans: false,
          data: null,
        };
      }

      // Create authentication token for Finsire API
      const token = `${env.FINSIRE_CLIENT_ID}:${env.FINSIRE_CLIENT_SECRET}`;
      const encryptedToken = Buffer.from(token)
        .toString("base64")
        .replace(/\+/g, "-")
        .replace(/\//g, "_")
        .replace(/=+$/, "");

      // Call Finsire API to get user's loan data
      const response = await fetch(
        `https://dsa.finsire.com/v1/api/dsa/disbursments/list_overdraft`,
        // `https://dsa.finsire.com/v1/api/dsa/users/${user.finsireId}`,
        {
          method: "POST",
          body: JSON.stringify({
            list_overdrafts: {
              finsire_id: user.finsireId,
            },
          }),
          headers: {
            Authorization: encryptedToken,
          },
        },
      );

      if (!response.ok) {
        console.log("Finsire API error:", await response.text());
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: `Failed to fetch loan data: ${response.status} ${response.statusText}`,
        });
      }

      const result = (await response.json()) as FinsireLoanResponse;

      console.log("result is", result);

      if (!result.status) {
        return {
          hasLoans: false,
          data: null,
        };
      }

      return {
        hasLoans: true,
        data: result.data,
      };
    } catch (err) {
      if (err instanceof TRPCError) {
        throw err;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "An error occurred while fetching loan data",
        cause: err,
      });
    }
  }),
} satisfies TRPCRouterRecord;
