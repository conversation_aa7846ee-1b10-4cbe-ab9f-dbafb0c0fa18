import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";

import { eq } from "@acme/db";
import { customer } from "@acme/db/schema";
import { LoanApplicationFormSchema } from "@acme/validators/customer";

import { env } from "~/env";
import { protectedProcedure, publicProcedure } from "../trpc";

export const dashboardRouter = {
  getAllFaqs: publicProcedure.query(async ({ ctx }) => {
    try {
      const faqs = await ctx.db.query.faq.findMany({});

      return faqs;
    } catch (err) {
      if (err instanceof TRPCError) {
        throw err;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "An error occurred while fetching FAQs",
        cause: err,
      });
    }
  }),

  applyLoan: protectedProcedure
    .input(LoanApplicationFormSchema)
    .mutation(async ({ input, ctx }) => {
      try {
        // Create authentication token for Finsire API
        const token = `${env.FINSIRE_CLIENT_ID}:${env.FINSIRE_CLIENT_SECRET}`;
        const encryptedToken = Buffer.from(token)
          .toString("base64")
          .replace(/\+/g, "-")
          .replace(/\//g, "_")
          .replace(/=+$/, "");

        console.log("encryptedToken", encryptedToken);

        // Prepare the API payload
        const payload = {
          user: {
            name: input.name,
            phone: input.phone,
            pan: input.pan,
            latitude: input.latitude,
            longitude: input.longitude,
            email: input.email,
            "Aadhar Number": `00000000${input.dsaUniqueId}`,
          },
        };

        console.log("HTTP URL is", `${env.FINSIRE_API_URL}/assets_selection`);

        // Call Finsire API
        const response = await fetch(
          `${env.FINSIRE_API_URL}/assets_selection`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: encryptedToken,
            },
            body: JSON.stringify(payload),
          },
        );

        if (!response.ok) {
          console.log("result is", await response.text());
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: `Finsire API error: ${response.status} ${response.statusText}`,
          });
        }

        const result = (await response.json()) as {
          link: string;
          finsire_id: string;
          status: boolean;
          message: string;
          summary_link: null;
          widthdrawal_link: null;
        };

        await ctx.db
          .update(customer)
          .set({
            finsireId: result.finsire_id,
            finsireResponse: result,
          })
          .where(eq(customer.id, ctx.session.id!));

        return {
          success: true,
          finsireLink: result.link,
          message: "Loan application submitted successfully",
        };
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "An error occurred while submitting the loan application",
          cause: err,
        });
      }
    }),
} satisfies TRPCRouterRecord;
