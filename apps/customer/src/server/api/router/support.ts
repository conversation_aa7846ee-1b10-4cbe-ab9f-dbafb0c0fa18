import type { TRPCRouterRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { customerSupportMessage, issueType } from "@acme/db/schema";

import { protectedProcedure } from "../trpc";

export const supportRouter = {
  getCurrentSupportMessage: protectedProcedure.query(async ({ ctx }) => {
    try {
      const message = await ctx.db.query.customerSupportMessage.findFirst({
        where: eq(customerSupportMessage.customerId, ctx.session.id!),
      });

      return message;
    } catch (error) {
      console.error(error);
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch support message",
      });
    }
  }),

  createSupportMessage: protectedProcedure
    .input(
      z.object({
        message: z.string().max(600 * 5), // Approximately 600 words (5 chars per word avg)
        fullName: z.string(),
        email: z.string().email(),
        phone: z.string(),
        issueType: z.enum(issueType.enumValues),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { message, fullName, email, phone, issueType } = input;

      try {
        const existingMessage =
          await ctx.db.query.customerSupportMessage.findFirst({
            where: eq(customerSupportMessage.customerId, ctx.session.id!),
          });
        if (existingMessage) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "You have already created a support message",
          });
        }
        await ctx.db.insert(customerSupportMessage).values({
          fullName,
          email,

          phone,
          message,
          issueType,
          customerId: ctx.session.id!,
        });
        return {
          success: true,
          message: "Support message created successfully",
        };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create support message",
        });
      }
    }),
} satisfies TRPCRouterRecord;
