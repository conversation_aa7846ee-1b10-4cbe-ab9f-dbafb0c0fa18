import type { TRP<PERSON>outerRecord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { customer } from "@acme/db/schema";

import { env } from "~/env";
import { protectedProcedure } from "../trpc";

// PAN verification API response types
interface PanVerificationAddress {
  address_line_1: string;
  address_line_2: string;
  address_line_3: string;
  address_line_4: string;
  address_line_5: string;
  pin_code: string;
  state: string;
}

interface PanVerificationResult {
  pan: string;
  first_name: string;
  middle_name: string | null;
  last_name: string;
  full_name: string;
  gender: string;
  dob: string;
  email: string | null;
  mobile: string | null;
  aadhaar: string | null;
  aadharlink: boolean;
  tax: string;
  address: PanVerificationAddress;
  category: string;
}

interface PanVerificationResponse {
  api_category: string;
  api_name: string;
  billable: boolean;
  txn_id: string;
  message: string;
  status: string;
  result: PanVerificationResult;
  datetime: string;
}

export const profileRouter = {
  getProfile: protectedProcedure.query(async ({ ctx }) => {
    try {
      const user = await ctx.db.query.customer.findFirst({
        where: eq(customer.id, ctx.session.id!),
        columns: {
          id: true,
          name: true,
          email: true,
          phone: true,
          panNumber: true,
          dsaUniqueId: true,
          finsireId: true,
        },
      });
      console.log("user", user);

      return user;
    } catch (error) {
      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "Failed to fetch profile",
      });
    }
  }),
  updateProfile: protectedProcedure
    .input(
      z.object({
        name: z.string(),
        email: z.string().email(),
        panNumber: z.string().optional(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { name, email, panNumber } = input;
        const existingUser = await ctx.db.query.customer.findFirst({
          where: eq(customer.id, ctx.session.id!),
        });

        if (!existingUser) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "User not found",
          });
        }

        // Check if PAN number has changed and needs verification
        const panChanged = panNumber && panNumber !== existingUser.panNumber;

        let verifiedPanResult = null;

        // Verify PAN if it has changed
        if (panChanged) {
          try {
            const response = await fetch(
              "https://pan-verification-supreme.befisc.com/v2",
              {
                method: "POST",
                headers: {
                  authkey: env.BEFISC_API_KEY,
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ pan: panNumber }),
              },
            );

            if (!response.ok) {
              throw new Error(`API returned status: ${response.status}`);
            }

            const data = (await response.json()) as PanVerificationResponse;

            if (data.status !== "1") {
              throw new Error(`Verification failed: ${data.message}`);
            }

            verifiedPanResult = data.result;
          } catch (error) {
            throw new TRPCError({
              code: "BAD_REQUEST",
              message:
                error instanceof Error
                  ? `PAN verification failed: ${error.message}`
                  : "PAN verification failed: Please enter a valid PAN number",
            });
          }
        }

        // Update user data
        const updateData: any = {
          name,
          email,
        };

        // Only update PAN if verification was successful
        if (panChanged && verifiedPanResult) {
          updateData.panNumber = panNumber;
          updateData.panVerifiedResult = verifiedPanResult;
        }

        const updatedUser = await ctx.db
          .update(customer)
          .set(updateData)
          .where(eq(customer.id, ctx.session.id!));

        return updatedUser;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to update profile",
        });
      }
    }),
} satisfies TRPCRouterRecord;
