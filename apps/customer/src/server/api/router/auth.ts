import type { TRP<PERSON><PERSON>erR<PERSON>ord } from "@trpc/server";
import { TRPCError } from "@trpc/server";
import { ID } from "node-appwrite";
import { z } from "zod";

import { eq } from "@acme/db";
import { customer } from "@acme/db/schema";
import { OnboardingFormSchema } from "@acme/validators/customer";

import { env } from "~/env";
import { createAdminClient } from "~/server/auth/appwrite";
import { protectedProcedure, publicProcedure } from "../trpc";

// PAN verification API response types
interface PanVerificationAddress {
  address_line_1: string;
  address_line_2: string;
  address_line_3: string;
  address_line_4: string;
  address_line_5: string;
  pin_code: string;
  state: string;
}

interface PanVerificationResult {
  pan: string;
  first_name: string;
  middle_name: string | null;
  last_name: string;
  full_name: string;
  gender: string;
  dob: string;
  email: string | null;
  mobile: string | null;
  aadhaar: string | null;
  aadharlink: boolean;
  tax: string;
  address: PanVerificationAddress;
  category: string;
}

interface PanVerificationResponse {
  api_category: string;
  api_name: string;
  billable: boolean;
  txn_id: string;
  message: string;
  status: string;
  result: PanVerificationResult;
  datetime: string;
}

export const authRouter = {
  getOnboardingCompleted: protectedProcedure.query(async ({ ctx }) => {
    const customerData = await ctx.db.query.customer.findFirst({
      where: eq(customer.appwriteId, ctx.session.appwriteId!),
    });
    return customerData?.panNumber ? true : false;
  }),
  getLoggedUser: protectedProcedure.query(async ({ ctx }) => {
    const user = await ctx.db.query.customer.findFirst({
      where: eq(customer.appwriteId, ctx.session.appwriteId!),
    });
    return {
      userData: {
        appwriteId: user?.appwriteId,
        id: user?.id,
        phone: user?.phone,
        name: user?.name,
        dateOfBirth: user?.dateOfBirth,
        email: user?.email,
        dob: user?.dateOfBirth,
        isPhoneVerified: user?.isPhoneVerified,
        isPanVerified: user?.panVerifiedResult ? true : false,
      },
    };
  }),
  sendOtp: publicProcedure
    .input(z.object({ phoneNumber: z.string() }))
    .mutation(async ({ input }) => {
      const { phoneNumber } = input;
      try {
        const { account } = await createAdminClient();
        const token = await account.createEmailToken(
          ID.unique(),
          "<EMAIL>",
        );
        // const token = await account.createPhoneToken(
        //   ID.unique(),
        //   `+91${phoneNumber}`,
        // );
        return token;
      } catch (error) {
        console.error("error is", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to send OTP",
        });
      }
    }),
  verifyOtp: publicProcedure
    .input(
      z.object({
        otp: z.string(),
        userId: z.string(),
        phoneNumber: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { otp, userId, phoneNumber } = input;
      if (!userId || !otp || !phoneNumber) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Missing required fields",
        });
      }
      try {
        const { account } = await createAdminClient();
        console.log("userId is", userId);
        console.log("otp is", otp);
        console.log("account is", account);

        const session = await account.createSession(userId, otp);
        console.log("session is", session);

        ctx.setCookieFunc("appwrite-session", session.secret, {
          path: "/",
          httpOnly: true,
          sameSite: "strict",
          secure: true,
        });

        // Check if a user with this phone number already exists
        const existingUser = await ctx.db.query.customer.findFirst({
          where: eq(customer.phone, `+91${phoneNumber}`),
        });

        let isNewUser = false;

        if (existingUser) {
          // If the user exists, update their appwriteId and return
          await ctx.db
            .update(customer)
            .set({
              appwriteId: session.userId,
              isPhoneVerified: true,
            })
            .where(eq(customer.id, existingUser.id));
          isNewUser = false;
        } else {
          // If the user doesn't exist, create a new one
          await ctx.db.insert(customer).values({
            appwriteId: session.userId,
            isPhoneVerified: true,
            phone: `+91${phoneNumber}`,
          });
          isNewUser = true;
        }

        return {
          success: true,
          message: "Phone verified successfully",
          isNewUser,
        };
      } catch (error) {
        console.log("error is", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to verify phone number",
        });
      }
    }),
  verifyPan: protectedProcedure
    .input(OnboardingFormSchema)
    .mutation(async ({ ctx, input }) => {
      const { panCard } = input;

      try {
        const response = await fetch(
          "https://pan-verification-supreme.befisc.com/v2",
          {
            method: "POST",
            headers: {
              authkey: env.BEFISC_API_KEY,
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ pan: panCard }),
          },
        );
        if (!response.ok) {
          throw new Error(`API returned status: ${response.status}`);
        }

        const data = (await response.json()) as PanVerificationResponse;
        if (data.status !== "1") {
          throw new Error(`Verification failed: ${data.message}`);
        }

        await ctx.db
          .update(customer)
          .set({
            name: input.fullName,
            panNumber: input.panCard,
            dateOfBirth: input.dateOfBirth,
            panVerifiedResult: data.result,
          })
          .where(eq(customer.appwriteId, ctx.session.appwriteId ?? ""));
        return {
          success: true,
          message: "PAN verified successfully",
        };
      } catch (error) {
        console.error("PAN verification error:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message:
            error instanceof Error ? error.message : "Failed to verify PAN",
        });
      }
    }),
} satisfies TRPCRouterRecord;
