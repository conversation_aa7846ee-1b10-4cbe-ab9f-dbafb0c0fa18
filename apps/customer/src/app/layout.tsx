import "@acme/ui/globals.css";

import type { Metadata, Viewport } from "next";
import { Inter } from "next/font/google";

import { Toaster } from "@acme/ui/components/ui/sonner";

import { TRPCReactProvider } from "~/trpc/react";

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

export const metadata: Metadata = {
  title: "Customer Dashboard",
  description: "FluidPe Customer Dashboard",
  openGraph: {
    title: "FluidPe",
    description: "FluidPe Customer Dashboard",
  },
  icons: [{ rel: "icon", url: "/static/logos/day-logo.png" }],
};

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link
          rel="preload"
          href="/static/images/auth-layout-bg-image-80.webp"
          as="image"
        />
      </head>
      <body
        className={`${inter.className} font-inter min-h-screen w-full bg-background text-foreground antialiased`}
      >
        <TRPCReactProvider>
          <>{props.children}</>
          <Toaster richColors position="top-center" />
        </TRPCReactProvider>
      </body>
    </html>
  );
}
