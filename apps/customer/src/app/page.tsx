import { redirect } from "next/navigation";

import { getLoggedInUser } from "~/server/auth/appwrite";
import { api } from "~/trpc/server";

const page = async () => {
  const session = await getLoggedInUser();
  if (session?.phoneVerification === false || !session) {
    return redirect("/login");
  }
  const res = await api.auth.getOnboardingCompleted();
  if (res) {
    return redirect("/dashboard");
  }
  return (
    <h1>
      Welcome
      {JSON.stringify(session)}
    </h1>
  );
};

export default page;
