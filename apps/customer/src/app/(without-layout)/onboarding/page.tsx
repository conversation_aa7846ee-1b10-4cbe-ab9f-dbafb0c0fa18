"use client";

import type { z } from "zod";
import { useRouter } from "next/navigation";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { OnboardingFormSchema } from "@acme/validators/customer";

import { api } from "~/trpc/react";

const OnboardingPage = () => {
  const router = useRouter();
  const form = useForm<z.infer<typeof OnboardingFormSchema>>({
    resolver: zodResolver(OnboardingFormSchema),
    defaultValues: {
      fullName: "",
      dateOfBirth: "",
      panCard: "",
    },
  });
  const completeOnboarding = api.auth.verifyPan.useMutation({
    onSuccess: () => {
      router.replace("/onboarding/success");
    },
  });

  function onSubmit(values: z.infer<typeof OnboardingFormSchema>) {
    completeOnboarding.mutate({
      panCard: values.panCard,
      fullName: values.fullName,
      dateOfBirth: values.dateOfBirth,
    });

    // Here you would typically submit the data to an API
    // and then redirect the user to the next step
  }

  return (
    <div className="container mx-auto my-auto w-full">
      <div className="flex flex-col gap-[50px]">
        {/* heading */}
        <div className="flex flex-col items-center gap-2">
          <h2 className="text-3xl font-bold -tracking-[0.225px] text-fluidpe-teal">
            Complete Your Profile
          </h2>
          <h3 className="text-center text-lg text-zinc-500">
            Please provide your details to continue
          </h3>
        </div>

        {/* form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="flex flex-col gap-5">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Full Name</FormLabel>
                    <FormControl>
                      <Input placeholder="John Doe" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dateOfBirth"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Date of Birth</FormLabel>
                    <FormControl>
                      <Input type="date" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="panCard"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">PAN Card Number</FormLabel>
                    <FormControl>
                      <Input placeholder="**********" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button
              type="submit"
              variant="default"
              disabled={completeOnboarding.isPending}
              className="w-full px-6 py-3.5 text-base"
            >
              {completeOnboarding.isPending ? "Verifying..." : "Continue"}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default OnboardingPage;
