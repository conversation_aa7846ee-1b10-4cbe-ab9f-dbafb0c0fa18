"use client";

import { useRouter } from "next/navigation";
import { motion } from "motion/react";

import { Button } from "@acme/ui/components/ui/button";

export default function OnboardingSuccessPage() {
  const router = useRouter();
  return (
    <div className="container mx-auto my-auto flex w-full flex-col items-center justify-center self-center bg-neutral-50">
      <div className="inside flex w-fit flex-col items-center justify-center gap-8 self-center overflow-hidden rounded-3xl border border-[6px] border-emerald-800/10 bg-white stroke-slate-100 p-[50px] pt-[80px]">
        <motion.div
          className="relative h-[123px] w-[123px]"
          initial={{
            opacity: 0.3,
            scale: 0.6,
          }}
          animate={{
            opacity: 1,
            scale: 1,
          }}
          transition={{
            duration: 1,
            ease: "easeInOut",
          }}
        >
          <motion.img
            initial={{ rotate: 0 }}
            animate={{ rotate: 120 }}
            transition={{
              duration: 1,
              repeat: 0,
              ease: "easeInOut",
            }}
            src="/static/icons/cookie.png"
            alt="Onboarding Success"
            className="h-fit w-fit object-cover"
          />
          <motion.img
            src="/static/icons/check.png"
            alt="Onboarding Success"
            className="absolute -right-6 -top-8 h-fit w-fit object-cover"
          />
        </motion.div>
        <div className="flex flex-col items-center justify-center gap-8">
          <div className="flex flex-col items-center justify-center gap-2">
            <h1 className="text-[32px] font-extrabold leading-[42px] tracking-[-0.75%] text-emerald-800">
              Verified
            </h1>
            <p className="text-xl text-neutral-600">
              Continue your journey with <b>fluidpe</b>
            </p>
          </div>
          <Button
            onClick={() => router.replace("/")}
            className="w-full rounded-xl bg-black py-[14px]"
          >
            Continue
          </Button>
        </div>
      </div>
    </div>
  );
}
