"use client";

import type { z } from "zod";
import { Suspense, useEffect, useState } from "react";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { ChevronLeft } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@acme/ui/components/ui/input-otp";
import { OTPVerificationFormSchema } from "@acme/validators/customer";

import { api } from "~/trpc/react";

const OTPVerification = () => {
  const params = useSearchParams();
  const userId = params.get("userId");
  const expiresAt = params.get("expire");
  const phoneNumber = params.get("phoneNumber");
  const router = useRouter();
  const [remainingTime, setRemainingTime] = useState<number>(1);
  console.log("remainingTime", remainingTime);
  const form = useForm<z.infer<typeof OTPVerificationFormSchema>>({
    resolver: zodResolver(OTPVerificationFormSchema),
    defaultValues: {
      otp: "",
    },
  });

  const verifyOtpMutation = api.auth.verifyOtp.useMutation({
    onSuccess(data) {
      // If this is the user's first login (new user), redirect to onboarding
      // Otherwise, redirect to dashboard
      toast.success("OTP verified successfully");
      if (data.isNewUser) {
        router.replace("/onboarding");
      } else {
        router.replace("/dashboard");
      }
    },
    onError(error) {
      toast.error(error.message, {
        description: "Please check otp and try again",
      });
    },
  });

  function onSubmit(values: z.infer<typeof OTPVerificationFormSchema>) {
    if (!userId || !phoneNumber) {
      alert("User ID or phone number not found");
      return;
    }
    verifyOtpMutation.mutate({
      otp: values.otp,
      userId: userId,
      phoneNumber: phoneNumber,
    });
  }
  const handleBack = () => {
    router.back();
  };
  const sendOtpMutation = api.auth.sendOtp.useMutation({
    onSuccess({ userId, expire }) {
      router.replace(
        `/otp-verification?userId=${userId}&expire=${expire}&phoneNumber=${phoneNumber}`,
      );
    },
  });
  const handleResendOtp = () => {
    if (!userId || !phoneNumber) {
      alert("User ID or phone number not found");
      return;
    }
    sendOtpMutation.mutate({ phoneNumber: phoneNumber });
  };

  const handleTimeChange = (time: number) => {
    setRemainingTime(time);
  };

  return (
    <div className="my-auto w-full pr-[120px]">
      <div className="flex flex-col gap-[50px]">
        {/* heading */}
        <div className="flex flex-col gap-6">
          {/* back button */}
          <Button onClick={handleBack} variant="ghost" className="w-fit">
            <ChevronLeft className="h-4 w-4" />
            Change Number
          </Button>
          <div className="flex flex-col items-center gap-2">
            <h2 className="text-3xl font-bold -tracking-[0.225px] text-fluidpe-teal">
              Verify OTP
            </h2>
            <h3 className="text-center text-lg text-zinc-500">
              Enter the 5 digit otp that has been sent to your phone number{" "}
              <span className="font-semibold text-stone-700">
                +91
                {phoneNumber}
              </span>{" "}
              via message or sms
            </h3>
          </div>
        </div>
        {/* form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="flex flex-col gap-3.5">
              <FormField
                control={form.control}
                name="otp"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Enter code</FormLabel>
                    <FormControl>
                      <InputOTP
                        maxLength={6}
                        {...field}
                        // pattern={REGEXP_ONLY_DIGITS_AND_CHARS}
                      >
                        <InputOTPGroup className="gap-x-6">
                          <InputOTPSlot index={0} />
                          <InputOTPSlot index={1} />
                          <InputOTPSlot index={2} />
                          <InputOTPSlot index={3} />
                          <InputOTPSlot index={4} />
                          <InputOTPSlot index={5} />
                        </InputOTPGroup>
                      </InputOTP>
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
              <p className="text-sm text-slate-500">
                By proceeding, you agree to our{" "}
                <Link href="/terms-of-services" className="text-emerald-900">
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link href="/privacy-poilcy" className="text-emerald-900">
                  Privacy Policy
                </Link>
              </p>
            </div>
          </form>
        </Form>
        <div className="flex justify-between">
          {" "}
          <Button
            disabled={verifyOtpMutation.isPending}
            onClick={form.handleSubmit(onSubmit)}
            type="submit"
            variant={"default"}
            className="w-[213px] bg-black px-6 py-3.5 text-base"
          >
            {verifyOtpMutation.isPending ? "Verifying..." : "Verify OTP"}
          </Button>
          <Button
            onClick={handleResendOtp}
            disabled={sendOtpMutation.isPending || remainingTime > 0}
            variant="outline"
            className="w-[213px] px-6 py-3.5 text-base"
          >
            {remainingTime > 0 ? (
              <>
                Resend OTP in{" "}
                <OtpTimer date={expiresAt!} onTimeChange={handleTimeChange} />
              </>
            ) : (
              "Resend OTP"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

const SuspenseOTPVerification = () => {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <OTPVerification />
    </Suspense>
  );
};

export default SuspenseOTPVerification;

const OtpTimer = ({
  date,
  onTimeChange,
}: {
  date: string;
  onTimeChange: (time: number) => void;
}) => {
  const [time, setTime] = useState(0);
  useEffect(() => {
    const interval = setInterval(() => {
      const time = Math.max(
        0,
        new Date(date.replace(" ", "+")).getTime() - Date.now(),
      );
      setTime(time);
      onTimeChange(time);
    }, 1000);
    return () => clearInterval(interval);
  }, [date, onTimeChange]);

  const minutes = Math.floor((time / 1000 / 60) % 60);
  const seconds = Math.floor((time / 1000) % 60);

  return (
    <span>
      {minutes}:{seconds < 10 ? `0${seconds}` : seconds}s
    </span>
  );
};
