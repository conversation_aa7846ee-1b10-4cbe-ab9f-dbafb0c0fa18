"use client";

import type { z } from "zod";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { LoginFormSchema } from "@acme/validators/customer";

import { api } from "~/trpc/react";

const Login = () => {
  const router = useRouter();
  const { data: userData } = api.auth.getLoggedUser.useQuery();
  if (userData) {
    router.push("/dashboard");
  }
  const form = useForm<z.infer<typeof LoginFormSchema>>({
    resolver: zodResolver(LoginFormSchema),
    defaultValues: {
      phoneNumber: "",
    },
  });
  const sendOtpMutation = api.auth.sendOtp.useMutation({
    onSuccess({ userId, expire }) {
      router.push(
        `/otp-verification?userId=${userId}&expire=${expire}&phoneNumber=${form.getValues("phoneNumber")}`,
      );
    },
    onError(error) {
      console.error("error is", error);
      toast.error("something went wrong please try again later");
    },
  });
  const onSubmit = (data: z.infer<typeof LoginFormSchema>) => {
    sendOtpMutation.mutate(data);
  };

  return (
    <div className="my-auto w-full pr-[120px]">
      <div className="flex flex-col gap-[50px]">
        {/* heading */}
        <div className="flex flex-col items-start gap-2">
          <h2 className="text-left text-3xl font-bold -tracking-[0.225px] text-fluidpe-teal">
            Get started with Fluidpe
          </h2>
          <h3 className="text-left text-lg text-zinc-500">
            Enter your phone number below to start your finance journey
          </h3>
        </div>
        {/* form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <div className="flex flex-col gap-3.5">
              <FormField
                control={form.control}
                name="phoneNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Phone Number</FormLabel>
                    <FormControl>
                      <Input
                        className="px-4 py-3.5 text-sm leading-6"
                        placeholder="eg: 9876543210"
                        {...field}
                      />
                    </FormControl>

                    <FormMessage />
                  </FormItem>
                )}
              />
              <p className="text-sm text-slate-500">
                By proceeding, you agree to our{" "}
                <Link href="/terms-of-services" className="text-emerald-900">
                  Terms of Service
                </Link>{" "}
                and{" "}
                <Link href="/privacy-poilcy" className="text-emerald-900">
                  Privacy Policy
                </Link>
              </p>
            </div>
            <Button type="submit" className="w-[213px] px-6 py-3.5 text-base">
              Send OTP
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default Login;
