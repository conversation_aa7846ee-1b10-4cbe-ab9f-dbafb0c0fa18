import Image from "next/image";
import { redirect } from "next/navigation";

import { AUTH_LAYOUT_CARDS } from "~/lib/constants";
import { getLoggedInUser } from "~/server/auth/appwrite";

export default async function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getLoggedInUser();
  if (user) {
    redirect("/dashboard");
  }
  return (
    <div className="flex min-h-screen w-full flex-col gap-20 bg-neutral-50 lg:flex-row">
      {/* shared layout */}
      <div className="w-full bg-[url('/static/images/auth-layout-bg-image.webp')] bg-cover bg-center py-[100px] lg:min-w-[668px]">
        <div className="pl-[120px] pr-[42px]">
          <div className="flex flex-col gap-[100px]">
            {/* logo */}
            <div className="relative aspect-[58/69] w-[58px] self-start">
              <Image
                src="/static/logos/logo-white-without-bg.svg"
                alt="FluidPe Logo"
                fill
                className="relative object-cover"
              />
            </div>
            {/* text */}
            <div className="flex flex-col gap-10">
              {/* headings */}
              <div className="flex flex-col gap-2">
                <h1 className="text-5xl font-extrabold -tracking-[0.576px] text-white">
                  Fluidpe
                </h1>
                <h2 className="text-2xl leading-[38px] -tracking-[0.144px] text-neutral-100">
                  Financial solutions for your future
                </h2>
              </div>
              {/* cards */}
              <div className="flex flex-col items-start gap-6">
                {/* card */}
                {AUTH_LAYOUT_CARDS.map((card, i) => {
                  return (
                    <div
                      key={i}
                      className="flex gap-4 rounded-lg border-2 border-[rgba(9,143,131,0.60)] bg-[rgba(0,160,146,0.15)] px-6 py-4 backdrop-blur-[20px]"
                    >
                      {/* icon */}
                      <div className="relative aspect-square w-8">
                        <Image
                          src={card.icon}
                          alt="icon"
                          fill
                          className="relative object-cover"
                        ></Image>
                      </div>
                      {/* text */}
                      <p className="text-lg font-semibold text-green-100">
                        {card.text}
                      </p>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* children */}
      {children}
    </div>
  );
}
