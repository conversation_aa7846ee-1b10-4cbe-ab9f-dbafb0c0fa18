import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@acme/ui/components/ui/accordion";

import { api } from "~/trpc/server";

const FaqPage = async () => {
  const faq = await api.dashboard.getAllFaqs();
  return (
    <div className="flex flex-row gap-10 px-10 pb-[60px] pt-8">
      <div className="flex flex-col gap-4">
        <h1 className="text-nowrap text-2xl font-bold">
          Frequently Asked Questions
        </h1>
        <p className="text-sm text-gray-500">FAQ</p>
      </div>
      <Accordion type="single" collapsible className="w-full">
        {faq.map((item) => (
          <AccordionItem key={item.id} value={item.id} className="flex-1">
            <AccordionTrigger className="text-lg font-medium text-emerald-900">
              {item.question}
            </AccordionTrigger>
            <AccordionContent className="text-sm font-medium text-zinc-600">
              {item.answer}
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  );
};

export default FaqPage;
