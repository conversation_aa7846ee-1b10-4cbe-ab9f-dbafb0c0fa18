"use client";

import { useEffect, useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { But<PERSON> } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";

import { api } from "~/trpc/react";

// Form schema for the profile form
const ProfileFormSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().optional(),
  panNumber: z.string().optional(),
});

const ProfilePage = () => {
  const [isEdited, setIsEdited] = useState(false);
  const [originalData, setOriginalData] = useState<z.infer<
    typeof ProfileFormSchema
  > | null>(null);

  // Fetch user profile data
  const {
    data: profile,
    isLoading,
    refetch,
  } = api.profile.getProfile.useQuery();

  // Form setup
  const form = useForm<z.infer<typeof ProfileFormSchema>>({
    resolver: zodResolver(ProfileFormSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      phone: "",
      panNumber: "",
    },
    mode: "onChange",
  });

  // Update mutation
  const updateProfile = api.profile.updateProfile.useMutation({
    onSuccess: () => {
      setIsEdited(false);
      toast.success("Profile updated successfully");
      void refetch();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  // Set form values when profile data is loaded
  useEffect(() => {
    if (profile) {
      // Split name into first name and last name
      const nameParts = profile.name?.split(" ") || ["", ""];
      const firstName = nameParts[0] || "";
      const lastName = nameParts.slice(1).join(" ") || "";

      form.reset({
        firstName,
        lastName,
        email: profile.email || "",
        phone: profile.phone || "",
        panNumber: profile.panNumber || "",
      });

      setOriginalData({
        firstName,
        lastName,
        email: profile.email || "",
        phone: profile.phone || "",
        panNumber: profile.panNumber || "",
      });
    }
  }, [profile, form]);

  // Check if form values have been edited
  const watchFields = form.watch();
  useEffect(() => {
    if (originalData) {
      const isFirstNameChanged =
        watchFields.firstName !== originalData.firstName;
      const isLastNameChanged = watchFields.lastName !== originalData.lastName;
      const isEmailChanged = watchFields.email !== originalData.email;
      const isPanChanged = watchFields.panNumber !== originalData.panNumber;

      setIsEdited(
        isFirstNameChanged ||
          isLastNameChanged ||
          isEmailChanged ||
          isPanChanged,
      );
    }
  }, [watchFields, originalData]);

  function onSubmit(values: z.infer<typeof ProfileFormSchema>) {
    if (isEdited) {
      updateProfile.mutate({
        name: `${values.firstName} ${values.lastName}`.trim(),
        email: values.email,
        panNumber: values.panNumber,
      });
    }
  }

  // Prevent form submission on Enter key unless edited
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !isEdited) {
      e.preventDefault();
    }
  };

  if (isLoading) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  return (
    <div className="max-w-[820px] px-10 py-8">
      <div className="flex flex-col gap-[50px]">
        <div className="flex flex-col items-start gap-2">
          <h2 className="text-3xl font-bold -tracking-[0.225px] text-fluidpe-teal">
            My Profile
          </h2>
          <h3 className="text-center text-lg text-zinc-500">
            Manage your personal information
          </h3>
        </div>

        <div>
          <div>
            <div className="text-xl font-semibold text-fluidpe-teal">
              Personal Information
            </div>
          </div>
          <div>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
                onKeyDown={handleKeyDown}
              >
                <div className="flex flex-col gap-5">
                  <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="firstName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base">
                            First Name
                          </FormLabel>
                          <FormControl>
                            <Input
                              className="px-4 py-[14px] text-lg"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="lastName"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base">Last Name</FormLabel>
                          <FormControl>
                            <Input
                              className="px-4 py-[14px] text-lg"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-1 gap-5 md:grid-cols-2">
                    <FormField
                      control={form.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base">Email</FormLabel>
                          <FormControl>
                            <Input
                              className="px-4 py-[14px] text-lg"
                              type="email"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="phone"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-base">
                            Phone Number
                          </FormLabel>
                          <FormControl>
                            <Input
                              className="px-4 py-[14px] text-lg"
                              {...field}
                              disabled
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="panNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel className="text-base">PAN Number</FormLabel>
                        <FormControl>
                          <Input
                            className="px-4 py-[14px] text-lg"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {isEdited && (
                  <Button
                    type="submit"
                    variant="default"
                    disabled={updateProfile.isPending}
                    className="w-full px-6 py-3.5 text-base"
                  >
                    {updateProfile.isPending ? "Saving..." : "Save Changes"}
                  </Button>
                )}
              </form>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;
