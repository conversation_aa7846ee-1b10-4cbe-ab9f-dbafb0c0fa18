"use client";

import { useState } from "react";
import { use<PERSON><PERSON><PERSON> } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { issueType } from "@acme/db/schema";
import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { Textarea } from "@acme/ui/components/ui/textarea";

import { api } from "~/trpc/react";

// Form schema for the contact form
const ContactFormSchema = z.object({
  fullName: z.string().min(1, "Full name is required"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(1, "Phone number is required"),
  issueType: z.enum(issueType.enumValues, {
    errorMap: () => ({ message: "Please select an issue type" }),
  }),
  message: z
    .string()
    .min(1, "Message is required")
    .max(600 * 5, "Message is too long (max 600 words)"),
});

const issueTypeLabels: Record<(typeof issueType.enumValues)[number], string> = {
  [issueType.enumValues[0]]: "Payment Issues",
  [issueType.enumValues[1]]: "Account Issues",
  [issueType.enumValues[2]]: "Loan Issues",
  [issueType.enumValues[3]]: "General Inquiry",
  [issueType.enumValues[4]]: "Other Issues",
};

const ContactPage = () => {
  const router = useRouter();
  const [wordCount, setWordCount] = useState(0);

  // Fetch existing support message if any
  const supportMessage = api.support.getCurrentSupportMessage.useQuery();

  // Form setup
  const form = useForm<z.infer<typeof ContactFormSchema>>({
    resolver: zodResolver(ContactFormSchema),
    defaultValues: {
      fullName: "",
      email: "",
      phone: "",
      message: "",
    },
  });

  // Submit mutation
  const createSupportMessage = api.support.createSupportMessage.useMutation({
    onSuccess: () => {
      void supportMessage.refetch();
    },
  });

  function onSubmit(values: z.infer<typeof ContactFormSchema>) {
    createSupportMessage.mutate(values);
  }

  const handleMessageChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const words = e.target.value.trim().split(/\s+/).length;
    setWordCount(e.target.value.trim() === "" ? 0 : words);
    form.setValue("message", e.target.value);
  };

  if (supportMessage.isLoading) {
    return <div className="flex justify-center p-8">Loading...</div>;
  }

  // If user has already submitted a support ticket, show the details
  if (supportMessage.data) {
    return (
      <div className="container mx-auto max-w-2xl py-8">
        <Card>
          <CardHeader>
            <CardTitle className="text-2xl font-bold text-fluidpe-teal">
              Your Support Ticket
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-zinc-500">Full Name</p>
                <p className="text-base">{supportMessage.data.fullName}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-zinc-500">Email</p>
                <p className="text-base">{supportMessage.data.email}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-zinc-500">Phone</p>
                <p className="text-base">{supportMessage.data.phone}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-zinc-500">Issue Type</p>
                <p className="text-base">
                  {issueTypeLabels[supportMessage.data.issueType]}
                </p>
              </div>
            </div>
            <div>
              <p className="text-sm font-medium text-zinc-500">Message</p>
              <p className="whitespace-pre-wrap text-base">
                {supportMessage.data.message}
              </p>
            </div>
            <div className="pt-2">
              <p className="text-sm text-zinc-500">
                We'll get back to you as soon as possible. Thank you for your
                patience.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // If no existing support ticket, show the form
  return (
    <div className="px-10 py-8">
      <div className="flex flex-col gap-[50px]">
        <div className="flex flex-col items-start gap-2">
          <h2 className="text-3xl font-bold -tracking-[0.225px] text-fluidpe-teal">
            Contact Support
          </h2>
          <h3 className="text-center text-lg text-zinc-500">
            Let us know how we can help you
          </h3>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="w-full max-w-[820px] space-y-6"
          >
            <div className="flex w-full flex-col gap-5">
              <div className="flex w-full flex-row gap-5">
                <FormField
                  control={form.control}
                  name="fullName"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="text-base">Full Name</FormLabel>
                      <FormControl>
                        <Input
                          className="px-4 py-[14px] text-lg"
                          placeholder="John Doe"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="text-base">Email</FormLabel>
                      <FormControl>
                        <Input
                          className="px-4 py-[14px] text-lg"
                          type="email"
                          placeholder="<EMAIL>"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="flex w-full flex-row gap-5">
                <FormField
                  control={form.control}
                  name="phone"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="text-base">Phone Number</FormLabel>
                      <FormControl>
                        <Input
                          className="w-full px-4 py-[14px] text-lg"
                          placeholder="+91 9876543210"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="issueType"
                  render={({ field }) => (
                    <FormItem className="flex-1">
                      <FormLabel className="text-base">Issue Type</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl className="">
                          <SelectTrigger className="h-fit flex-1 px-4 py-[14px] text-lg">
                            <SelectValue placeholder="Select an issue type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {Object.entries(issueTypeLabels).map(
                            ([value, label]) => (
                              <SelectItem
                                key={value}
                                value={value}
                                className="px-4 py-[14px] text-lg"
                              >
                                {label}
                              </SelectItem>
                            ),
                          )}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="message"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Message</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Please describe your issue in detail..."
                        className="min-h-[150px] w-full max-w-[820px] resize-none px-4 py-[14px] text-lg"
                        {...field}
                        onChange={handleMessageChange}
                      />
                    </FormControl>
                    <div className="flex">
                      <span
                        className={`text-xs ${wordCount > 600 ? "text-red-500" : "text-zinc-500"}`}
                      >
                        {wordCount}/600 words
                      </span>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button
              type="submit"
              variant="default"
              disabled={createSupportMessage.isPending}
              className="w-fit px-6 py-3.5 text-base"
            >
              {createSupportMessage.isPending
                ? "Submitting..."
                : "Submit Support Request"}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default ContactPage;
