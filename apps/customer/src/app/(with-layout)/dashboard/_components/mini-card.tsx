import { Badge } from "@acme/ui/components/ui/badge";
import { cn } from "@acme/ui/lib/utils";

interface MiniCardProps {
  title: string;
  description: string;
  badgeText: string;
  icon: React.ReactNode;
  containerClassName?: string;
  titleClassName?: string;
  descriptionClassName?: string;
  badgeClassName?: string;
  iconClassName?: string;
}

export function MiniCard({
  title,
  description,
  badgeText,
  icon,
  containerClassName,
  titleClassName,
  descriptionClassName,
  badgeClassName,
  iconClassName,
}: MiniCardProps) {
  return (
    <div
      className={cn(
        `flex h-fit w-full flex-col gap-4 rounded-lg p-6 md:w-fit`,
        containerClassName,
      )}
    >
      <div className="flex items-center justify-between">
        <div className={cn(iconClassName)}>{icon}</div>
        <div>
          <Badge
            className={cn(
              `rounded-sm bg-[#DCFCE799] px-[10px] py-[6px] text-green-900 shadow-none`,
              badgeClassName,
            )}
          >
            {badgeText}
          </Badge>
        </div>
      </div>
      <div className="flex flex-col gap-1">
        <h1
          className={cn(
            "font-inter text-base font-bold text-[#282828]",
            titleClassName,
          )}
        >
          {title}
        </h1>
        <p
          className={cn(
            "font-inter text-sm font-medium text-neutral-600",
            descriptionClassName,
          )}
        >
          {description}
        </p>
      </div>
    </div>
  );
}
