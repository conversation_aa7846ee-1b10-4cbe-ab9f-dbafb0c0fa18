"use client";

import { useState } from "react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import LoanApplicationModal from "~/components/loan-application-modal";

export function CTAFooter() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <div className="flex w-full flex-row items-center justify-between rounded-3xl bg-[url('/static/images/cta-bg.png')] bg-cover bg-center bg-no-repeat px-[60px] py-10">
        <div className="flex flex-col gap-2">
          <h1 className="text-[30px] font-semibold leading-8 text-white">
            Get Instant Financial Flexibility
          </h1>
          <p className="text-base text-zinc-300">
            Lorep ispsum ben tir ekmek yer bir kadam sen{" "}
          </p>
        </div>
        <div>
          <Button
            className="rounded-[10px] bg-lime-300 px-6 py-[14px] text-black"
            onClick={() => setIsModalOpen(true)}
          >
            Apply & Get Cash
          </Button>
        </div>
      </div>

      <LoanApplicationModal open={isModalOpen} onOpenChange={setIsModalOpen} />
    </>
  );
}
