"use client";

import { useState } from "react";
import { ArrowUpRight } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import LoanApplicationModal from "~/components/loan-application-modal";

export function CTAInfo() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <div className="flex w-full flex-col items-center justify-between gap-6 rounded-lg bg-[url('/static/images/info.png')] bg-cover bg-center bg-no-repeat p-8 md:flex-row">
        <div className="flex flex-col items-center justify-between gap-6 md:items-start">
          <div className="flex flex-col gap-[14px]">
            <h1 className="text-[30px] font-bold leading-9 text-white">
              Instant Cash Against Mutual
            </h1>
            <p className="text-[16px] leading-6 text-slate-100">
              Unlock financial flexibility without selling your investments{" "}
            </p>
          </div>
          <div className="flex flex-row items-center gap-6">
            <Button
              className="bg-lime-300 text-black"
              onClick={() => setIsModalOpen(true)}
            >
              Get Cash <ArrowUpRight className="ml-2 h-4 w-4" />
            </Button>
            <p className="text-base font-semibold text-zinc-100">
              Loan up to ₹5 lakh
            </p>
          </div>
        </div>
        <div className="flex h-fit w-fit flex-row gap-6">
          <div className="flex flex-col items-center gap-2 rounded-2xl bg-black/40 p-4">
            <p className="text-nowrap text-sm text-gray-50">Credit card</p>
            <h1 className="text-2xl font-extrabold text-gray-100">35%</h1>
          </div>
          <div className="flex flex-col items-center gap-2 rounded-2xl bg-black/40 p-4">
            <p className="text-nowrap text-sm text-gray-50">Personal loan</p>
            <h1 className="text-2xl font-extrabold text-gray-100">18%</h1>
          </div>
        </div>
      </div>

      <LoanApplicationModal open={isModalOpen} onOpenChange={setIsModalOpen} />
    </>
  );
}
