"use client";

import { useState } from "react";
import { <PERSON><PERSON>ard, Clock, CheckCircle, Eye } from "lucide-react";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@acme/ui/components/ui/card";
import { Badge } from "@acme/ui/components/ui/badge";

import { api } from "~/trpc/react";
import LoanDetailsModal from "~/components/loan-details-modal";

interface LoanStatusProps {}

const LoanStatus = ({}: LoanStatusProps) => {
  const [selectedLoan, setSelectedLoan] = useState<any>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  const { data: loanData, isLoading } = api.dashboard.getUserLoans.useQuery();

  const handleViewDetails = (loan: any, type: string) => {
    setSelectedLoan({ ...loan, type });
    setIsModalOpen(true);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            My Loans
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex h-32 items-center justify-center">
            <div className="text-lg">Loading loan information...</div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!loanData?.hasLoans || !loanData.data) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            My Loans
          </CardTitle>
          <CardDescription>
            Your loan applications and active loans will appear here
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex h-32 items-center justify-center text-gray-500">
            No loan applications found
          </div>
        </CardContent>
      </Card>
    );
  }

  const { overdrafts } = loanData.data;

  return (
    <>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CreditCard className="h-5 w-5" />
            My Loans
          </CardTitle>
          <CardDescription>
            Track your loan applications and manage active loans
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Active Loans */}
          {overdrafts.active_overdrafts.length > 0 && (
            <div>
              <h3 className="mb-3 flex items-center gap-2 text-lg font-semibold text-green-700">
                <CheckCircle className="h-5 w-5" />
                Active Loans ({overdrafts.active_overdrafts.length})
              </h3>
              <div className="space-y-3">
                {overdrafts.active_overdrafts.map((loan) => (
                  <div
                    key={loan.loan_id}
                    className="flex items-center justify-between rounded-lg border border-green-200 bg-green-50 p-4"
                  >
                    <div>
                      <p className="font-medium">LAN: {loan.lan}</p>
                      <p className="text-sm text-gray-600">
                        Amount: ₹{loan.loan_amount?.toLocaleString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="default" className="bg-green-600">
                        Active
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(loan, "active")}
                      >
                        <Eye className="mr-1 h-4 w-4" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* In Progress Loans */}
          {overdrafts.inprogress_overdrafts.length > 0 && (
            <div>
              <h3 className="mb-3 flex items-center gap-2 text-lg font-semibold text-orange-700">
                <Clock className="h-5 w-5" />
                In Progress ({overdrafts.inprogress_overdrafts.length})
              </h3>
              <div className="space-y-3">
                {overdrafts.inprogress_overdrafts.map((loan) => (
                  <div
                    key={loan.loan_id}
                    className="flex items-center justify-between rounded-lg border border-orange-200 bg-orange-50 p-4"
                  >
                    <div>
                      <p className="font-medium">LAN: {loan.lan}</p>
                      <p className="text-sm text-gray-600">
                        Status: {loan.current_status}
                      </p>
                      <p className="text-xs text-gray-500">
                        Applied: {loan.created_at}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="bg-orange-100 text-orange-800">
                        {loan.current_status}
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(loan, "inprogress")}
                      >
                        <Eye className="mr-1 h-4 w-4" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Closed Loans */}
          {overdrafts.closed_overdrafts.length > 0 && (
            <div>
              <h3 className="mb-3 flex items-center gap-2 text-lg font-semibold text-gray-700">
                <CheckCircle className="h-5 w-5" />
                Closed Loans ({overdrafts.closed_overdrafts.length})
              </h3>
              <div className="space-y-3">
                {overdrafts.closed_overdrafts.map((loan) => (
                  <div
                    key={loan.loan_id}
                    className="flex items-center justify-between rounded-lg border border-gray-200 bg-gray-50 p-4"
                  >
                    <div>
                      <p className="font-medium">LAN: {loan.lan}</p>
                      <p className="text-sm text-gray-600">
                        Amount: ₹{loan.loan_amount?.toLocaleString()}
                      </p>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="bg-gray-100 text-gray-800">
                        Closed
                      </Badge>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewDetails(loan, "closed")}
                      >
                        <Eye className="mr-1 h-4 w-4" />
                        View
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <LoanDetailsModal
        loan={selectedLoan}
        open={isModalOpen}
        onOpenChange={setIsModalOpen}
      />
    </>
  );
};

export default LoanStatus;
