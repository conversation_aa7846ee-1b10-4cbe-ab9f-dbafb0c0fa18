import Image from "next/image";

import { Separator } from "@acme/ui/components/ui/separator";

export function ChooseUs() {
  return (
    <div className="flex h-full w-full flex-row justify-between gap-10 overflow-hidden rounded-lg border-2 border-slate-200 p-5 md:py-0 md:pl-0 md:pr-8">
      <Image
        src="/static/images/choose-us.png"
        className="hidden h-[510px] w-[231px] md:flex"
        alt="choose-us"
        height={510}
        width={231}
      />
      <div className="flex flex-col gap-6 p-5 md:px-0 md:pt-8">
        <h1 className="text-2xl font-bold text-zinc-900">Why Choose us</h1>
        <div className="flex flex-col gap-6">
          <div className="flex flex-col gap-3">
            <div>
              <Image
                src="/static/icons/digital-paper.png"
                alt="choose-us"
                height={40}
                width={40}
                className=""
              />
            </div>
            <div className="flex flex-col gap-2">
              <h1 className="text-base font-bold text-green-900">
                100% Digital Process
              </h1>
              <p className="text-sm font-medium text-zinc-600">
                Paperless application with instant processing
              </p>
            </div>
          </div>
          <Separator className="h-[3px] w-full bg-slate-100" />
          <div className="flex flex-col gap-3">
            <div>
              <Image
                src="/static/icons/paper-timer.svg"
                alt="choose-us"
                height={40}
                width={40}
              />
            </div>
            <div className="flex flex-col gap-2">
              <h1 className="text-base font-bold text-green-900">
                Zero Pre-Closure Charge{" "}
              </h1>
              <p className="text-sm font-medium text-zinc-600">
                Complete flexibility in loan repayment{" "}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
