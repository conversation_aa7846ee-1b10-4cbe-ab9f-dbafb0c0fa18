export function HowItWorks() {
  return (
    <div className="flex w-full flex-col gap-6 rounded-2xl border border-2 border-slate-200 p-8">
      <div className="flex flex-col gap-[14px]">
        <h1 className="text-2xl font-bold text-zinc-900">How it works</h1>
        <p className="text-sm font-medium text-neutral-500">
          Loan Against Securities (LAS) is a secure way to leverage your
          investment. Pledge your mututal funds or stocks as collateral and
          access funds without disrupting your investment stragegy
        </p>
      </div>
      <div className="flex flex-col gap-4 lg:min-w-[452px]">
        <div className="flex w-fit flex-row gap-3 rounded-md bg-[#C1C1C11A] p-3">
          <h1 className="text-2xl font-extrabold text-[#008133]/50">01</h1>
          <div className="flex flex-col gap-[6px]">
            <h4 className="text-sm font-bold text-green-900">Verification</h4>
            <p className="text-sm text-neutral-700">
              One-time online verification using PAN & Aadhar details
            </p>
          </div>
        </div>
        <div className="flex w-fit flex-row gap-3 rounded-md bg-[#C1C1C11A] p-3">
          <h1 className="text-2xl font-extrabold text-[#008133]/50">02</h1>
          <div className="flex flex-col gap-[6px]">
            <h4 className="text-sm font-bold text-green-900">
              Select mutual funds
            </h4>
            <p className="text-sm text-neutral-700">
              Select the MF for which you are going to take loan{" "}
            </p>
          </div>
        </div>{" "}
        <div className="flex w-fit flex-row gap-3 rounded-md bg-[#C1C1C11A] p-3">
          <h1 className="text-2xl font-extrabold text-[#008133]/50">03</h1>
          <div className="flex flex-col gap-[6px]">
            <h4 className="text-sm font-bold text-green-900">Apply for loan</h4>
            <p className="text-sm text-neutral-700">
              Verify your bank account & apply by lien mark your funds at CAMS{" "}
            </p>
          </div>
        </div>{" "}
        <div className="flex w-fit flex-row gap-3 rounded-md bg-[#C1C1C11A] p-3">
          <h1 className="text-2xl font-extrabold text-[#008133]/50">04</h1>
          <div className="flex flex-col gap-[6px]">
            <h4 className="text-sm font-bold text-green-900">
              Overdraft or immediate disbursal
            </h4>
            <p className="text-sm text-neutral-700 lg:text-nowrap">
              Whatever you apt for, you will receive your money within the same
              day{" "}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
