import { <PERSON>D<PERSON>, Rocket, <PERSON>Check } from "lucide-react";

import { ChooseUs } from "./_components/choose-us";
import { CTAFooter } from "./_components/cta-footer";
import { CTAInfo } from "./_components/cta-info";
import { HowItWorks } from "./_components/how-it-works";
import LoanStatus from "./_components/loan-status";
import { MiniCard } from "./_components/mini-card";

const Dashboard = () => {
  return (
    <div className="flex flex-col gap-6 px-6 py-[60px] pt-6">
      <CTAInfo />
      <LoanStatus />
      <div className="flex w-full flex-row flex-wrap gap-6">
        <MiniCard
          title="Low Interest Rate"
          description="Better rates than traditional personal loans"
          badgeText="Start @ 10% p.a."
          icon={<ArrowDown />}
          containerClassName="bg-[#F6F1FF]"
        />
        <MiniCard
          title="Secure & Transparent Process"
          description="Get loan value against your stock portfolio"
          badgeText="50% LTV"
          icon={<ShieldCheck />}
          containerClassName="bg-[#FFFAEF]"
        />{" "}
        <MiniCard
          title="Fast & Easy Application"
          description="Instant digital verification process"
          badgeText="+1.29%"
          icon={<Rocket />}
          containerClassName="bg-blue-50"
        />
      </div>
      <div className="flex w-full flex-col gap-10 lg:flex-row">
        <ChooseUs />
        <HowItWorks />
      </div>
      <CTAFooter />
    </div>
  );
};

export default Dashboard;
