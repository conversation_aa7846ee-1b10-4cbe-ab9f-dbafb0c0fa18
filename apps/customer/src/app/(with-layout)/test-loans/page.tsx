"use client";

import { api } from "~/trpc/react";

const TestLoansPage = () => {
  const { data: loanData, isLoading, error } = api.dashboard.getUserLoans.useQuery();

  if (isLoading) {
    return <div>Loading loan data...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Test Loans API</h1>
      <pre className="bg-gray-100 p-4 rounded">
        {JSON.stringify(loanData, null, 2)}
      </pre>
    </div>
  );
};

export default TestLoansPage;
