import { redirect } from "next/navigation";
import { BellIcon } from "lucide-react";

import { Input } from "@acme/ui/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@acme/ui/components/ui/popover";
import { Separator } from "@acme/ui/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@acme/ui/components/ui/sidebar";

import { AppSidebar } from "~/components/shared/app-sidebar";
import { getLoggedInUser } from "~/server/auth/appwrite";
import { api } from "~/trpc/server";

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getLoggedInUser();
  if (!user) {
    redirect("/login");
  }
  const { userData } = await api.auth.getLoggedUser();
  return (
    <SidebarProvider className="min-h-fit">
      <AppSidebar
        user={{
          name: userData.name ?? "",
          email: userData.email ?? userData.phone ?? "",
          avatar: "",
        }}
      />
      <SidebarInset>
        <header className="sticky top-0 flex shrink-0 items-center justify-between border-b bg-white px-9 py-[18px]">
          {/* left side */}
          <div className="flex items-center gap-2">
            <SidebarTrigger className="-ml-1 h-6" />

            <Separator orientation="vertical" className="mr-2 h-7" />
            <Input
              className="ml-2 w-[213px] px-3 py-2 text-base"
              placeholder="Search..."
            ></Input>
          </div>
          {/* right side */}
          <Popover>
            <PopoverTrigger className="cursor-pointer rounded-md bg-zinc-100 p-2">
              <BellIcon className="h-6 w-6" />
            </PopoverTrigger>
            <PopoverContent className="mr-10 w-[300px]">
              <div>Welcome to fluidpe </div>
            </PopoverContent>
          </Popover>
        </header>

        {children}
      </SidebarInset>
    </SidebarProvider>
  );
}
