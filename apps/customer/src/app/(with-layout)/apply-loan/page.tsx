"use client";

import type { z } from "zod";
import { useEffect } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { LoanApplicationFormSchema } from "@acme/validators/customer";

import { api } from "~/trpc/react";

const ApplyLoanPage = () => {
  // Fetch user profile data to prefill the form
  const { data: profile, isLoading: profileLoading } =
    api.profile.getProfile.useQuery();

  // Form setup
  const form = useForm<z.infer<typeof LoanApplicationFormSchema>>({
    resolver: zodResolver(LoanApplicationFormSchema),
    defaultValues: {
      name: "",
      phone: "",
      pan: "",
      email: "",
      aadharNumber: "",
    },
    mode: "onChange",
  });

  // Prefill form with user data when profile is loaded
  useEffect(() => {
    if (profile) {
      form.setValue("name", profile.name ?? "");
      form.setValue("phone", profile.phone ?? "");
      form.setValue("pan", profile.panNumber ?? "");
      form.setValue("email", profile.email ?? "");
    }
  }, [profile, form]);

  // Apply loan mutation
  const applyLoan = api.dashboard.applyLoan.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      form.reset();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const onSubmit = (values: z.infer<typeof LoanApplicationFormSchema>) => {
    applyLoan.mutate(values);
  };

  if (profileLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg">Loading...</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto max-w-2xl px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-fluidpe-teal">Apply for Loan</h1>
        <p className="mt-2 text-gray-600">
          Fill out the form below to apply for your loan. We'll process your
          application quickly and securely.
        </p>
      </div>

      <div className="rounded-lg bg-white p-6 shadow-md">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      Full Name *
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="px-4 py-3 text-lg"
                        placeholder="Enter your full name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      Phone Number *
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="px-4 py-3 text-lg"
                        placeholder="Enter your phone number"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="pan"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      PAN Number *
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="px-4 py-3 text-lg uppercase"
                        placeholder="**********"
                        {...field}
                        onChange={(e) =>
                          field.onChange(e.target.value.toUpperCase())
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base font-medium">
                      Email Address *
                    </FormLabel>
                    <FormControl>
                      <Input
                        className="px-4 py-3 text-lg"
                        placeholder="Enter your email address"
                        type="email"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="aadharNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-base font-medium">
                    Aadhar Number *
                  </FormLabel>
                  <FormControl>
                    <Input
                      className="px-4 py-3 text-lg"
                      placeholder="Enter your 12-digit Aadhar number"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4 pt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => form.reset()}
                disabled={applyLoan.isPending}
              >
                Reset Form
              </Button>
              <Button
                type="submit"
                disabled={applyLoan.isPending}
                className="bg-fluidpe-teal hover:bg-fluidpe-teal/90"
              >
                {applyLoan.isPending ? "Submitting..." : "Apply for Loan"}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default ApplyLoanPage;
