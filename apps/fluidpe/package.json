{"name": "@acme/fluidpe", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "pnpm with-env next build", "clean": "git clean -xdf .cache .next .turbo node_modules", "dev": "pnpm with-env next dev -p 3000 --turbopack", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "start": "pnpm with-env next start", "typecheck": "tsc --noEmit", "with-env": "dotenv -e ../../.env --"}, "prettier": "@acme/prettier-config", "dependencies": {"@acme/db": "workspace:*", "@acme/lexical-editor": "workspace:*", "@acme/ui": "workspace:*", "@acme/validators": "workspace:*", "@prisma/nextjs-monorepo-workaround-plugin": "catalog:", "@t3-oss/env-nextjs": "catalog:", "@tanstack/react-query": "catalog:", "@trpc/client": "catalog:", "@trpc/react-query": "catalog:", "@trpc/server": "catalog:", "geist": "catalog:", "next": "catalog:", "react": "catalog:react19", "react-dom": "catalog:react19", "superjson": "catalog:", "zod": "catalog:"}, "devDependencies": {"@acme/eslint-config": "workspace:*", "@acme/prettier-config": "workspace:*", "@acme/tailwind-config": "workspace:*", "@acme/tsconfig": "workspace:*", "@types/node": "catalog:", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "dotenv-cli": "catalog:", "eslint": "catalog:", "jiti": "catalog:", "prettier": "catalog:", "tailwindcss": "catalog:", "typescript": "catalog:"}}