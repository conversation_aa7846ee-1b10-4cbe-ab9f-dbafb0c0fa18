import {
  Facebook,
  Instagram,
  Linkedin,
  Mail,
  MapPin,
  Phone,
  Twitter,
} from "lucide-react";

export const testimonials = [
  {
    quote:
      "This solution transformed our workflow. We've seen a 50% increase in productivity since implementation.",
    author: "<PERSON>",
    designation: "Director of Operations, TechCorp",
  },
  {
    quote:
      "Intuitive interface and powerful features. It's exactly what our team needed to streamline our processes.",
    author: "<PERSON>",
    designation: "Product Manager, InnoSystems",
  },
  {
    quote:
      "The customer support is exceptional. They helped us customize the platform to meet our specific needs.",
    author: "<PERSON><PERSON>",
    designation: "CTO, GrowthLabs",
  },
  {
    quote:
      "We've reduced our operational costs by 30% after adopting this solution. The ROI has been remarkable.",
    author: "<PERSON>",
    designation: "Finance Director, OptimizeNow",
  },
  {
    quote:
      "The analytics dashboard provides insights we never had access to before. Game-changing for our decision making.",
    author: "<PERSON>",
    designation: "Data Analyst, InsightfulTech",
  },
  {
    quote:
      "Implementation was smooth and the learning curve was minimal. Our entire team was up and running in days.",
    author: "<PERSON>",
    designation: "HR <PERSON>, TalentForce",
  },
  {
    quote:
      "The automation features have eliminated repetitive tasks, allowing our team to focus on strategic initiatives.",
    author: "<PERSON>",
    designation: "Innovation Lead, FutureCorp",
  },
  {
    quote:
      "Scalable and reliable. As our company has grown, the platform has grown with us without any performance issues.",
    author: "Robert Taylor",
    designation: "IT Director, ScaleUp Inc.",
  },
  {
    quote:
      "The security features give us peace of mind when handling sensitive customer data. Compliance has never been easier.",
    author: "Aisha <PERSON>",
    designation: "Security Officer, TrustGuard",
  },
  {
    quote:
      "This platform has become the backbone of our operations. I can't imagine running our business without it now.",
    author: "Thomas Wilson",
    designation: "CEO, VisionaryGroup",
  },
];

export const FOOTER = {
  socialLinks: [
    { href: "/", icon: <Facebook size={20} /> },
    { href: "/", icon: <Twitter size={20} /> },
    { href: "/", icon: <Instagram size={20} /> },
    { href: "/", icon: <Linkedin size={20} /> },
  ],
  quickLinks: [
    { href: "/", label: "Home" },
    { href: "#features", label: "Features" },
    { href: "#how-it-works", label: "How It Works" },
    { href: "#benefits", label: "Benefits" },
    { href: "#testimonials", label: "Testimonials" },
    { href: "/blogs", label: "Blogs" },
    { href: "#faq", label: "FAQ" },
  ],

  resourceLinks: [
    { href: "/blogs", label: "Blog" },
    { href: "#benefits", label: "Calculators" },
    { href: "/terms-and-conditions", label: "Terms and Conditions" },
    { href: "/privacy-policy", label: "Privacy Policy" },
  ],

  contactInfo: [
    {
      icon: <MapPin className="mr-2 mt-0.5 h-5 w-5 text-gray-300" />,
      content:
        "Unit No.212 P No H3 Netaji, Subhash Place Pitam Pura, Rani Bagh, Delhi, North West Delhi- 110034, Delhi",
      href: null,
    },
    {
      icon: <Phone className="mr-2 h-5 w-5 text-gray-300" />,
      content: "+91 987175995, +91 9643839141",
      href: "tel:+91987175995",
    },
    {
      icon: <Mail className="mr-2 h-5 w-5 text-gray-300" />,
      content: "<EMAIL>",
      href: "mailto:<EMAIL>",
    },
  ],
};

export const TIME_SLOTS = [
  "9:00 AM - 10:00 AM",
  "10:00 AM - 11:00 AM",
  "11:00 AM - 12:00 PM",
  "1:00 PM - 2:00 PM",
  "2:00 PM - 3:00 PM",
  "3:00 PM - 4:00 PM",
  "4:00 PM - 5:00 PM",
];
