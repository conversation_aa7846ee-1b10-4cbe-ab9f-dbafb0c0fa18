import type { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import { api } from "@/trpc/server";
import { ArrowLeft, CalendarIcon } from "lucide-react";

import { db } from "@acme/db/client";

import BlogPreviewComponent from "./blog-preview";

interface BlogDetailsPageProps {
  params: Promise<{
    slug: string;
  }>;
}

export const revalidate = 600; // invalidate every 10 mins
export async function generateMetadata({
  params,
}: BlogDetailsPageProps): Promise<Metadata> {
  const { slug } = await params;
  const blogsData = await api.blog.getBlogByIdOrSlug({ slug });

  if (!blogsData) {
    return {
      title: "Blog Not Found",
      description: "The requested blog could not be found",
    };
  }

  const blog = blogsData;
  const blogImage = blog.imageUrl;
  const blogAuthor = blog.authorName;

  // Get the meta fields (these come from the SEO plugin)
  const metaImage = blogImage;

  return {
    title: blog.title || blog.title,
    description: blog.excerpt || blog.excerpt,
    openGraph: {
      type: "article",
      title: blog.title || blog.title,
      description: blog.excerpt || blog.excerpt,
      images: [
        {
          url: metaImage || "",
          alt: metaImage || blog.title,
        },
      ],
      authors: [blogAuthor || ""],
      publishedTime: new Date(blog.createdAt).toISOString() || "",
      modifiedTime: new Date(blog.updatedAt).toISOString() || "",
      tags: blog.tags || [],
    },
    twitter: {
      card: "summary_large_image",
      title: blog.title || blog.title,
      description: blog.excerpt || blog.excerpt,
      images: [metaImage || ""],
      creator: blogAuthor || "",
    },
  };
}

export async function generateStaticParams() {
  const blogsData = await db.query.blog.findMany();

  return blogsData.map((blog) => ({
    slug: blog.slug,
  }));
}

const Blogs = async ({ params }: BlogDetailsPageProps) => {
  const { slug } = await params;
  const blogsData = await api.blog.getBlogByIdOrSlug({ slug });

  return (
    <div className="flex w-full min-w-full flex-col px-10 pb-10 pt-20">
      {/* Blog titles and author - full width */}
      {/* <Heading title={blogsData[0].blogTitle} /> */}
      <div className="bg-secondary-50 w-full space-y-2">
        <div className="container mx-auto min-w-full space-y-2 pb-8 md:pb-9 lg:pb-[45px] xl:py-[60px]">
          <Link
            className="flex w-fit items-center justify-center gap-2 py-6"
            href="/blogs"
          >
            <ArrowLeft className="size-4" />
            Back to Blogs
          </Link>
          <div className="flex flex-col gap-5">
            <div className="flex flex-col gap-4">
              <h1 className="text-wrap text-left text-[32px] font-extrabold leading-9 md:text-4xl lg:text-[40px] lg:leading-[48px] xl:text-5xl 2xl:text-[56px] 2xl:leading-[70px]">
                {blogsData?.title}
              </h1>
              <div className="flex w-full flex-wrap justify-start gap-5">
                {blogsData?.tags?.map((keyword) => (
                  <div
                    className="rounded-md bg-emerald-500 px-4 py-2 text-emerald-100"
                    key={keyword}
                  >
                    {keyword}
                  </div>
                ))}
              </div>
            </div>
            <div className="flex flex-col items-start justify-start gap-3.5 md:flex-row md:items-center md:justify-between">
              <div className="size- flex w-full items-center justify-start gap-3">
                <div className="flex flex-col items-start justify-between self-stretch">
                  <div className="font-roboto justify-start self-stretch text-base font-semibold leading-normal text-[#212121]">
                    {blogsData?.authorName}
                  </div>
                </div>
              </div>
              <div className="flex w-full items-center justify-between md:w-fit md:gap-5 lg:gap-[35px] xl:gap-[60px]">
                <div className="size- flex items-center justify-start gap-1">
                  <div className="bg-secondary-main relative size-5 overflow-hidden rounded-full"></div>
                  <div className="font-roboto justify-start text-nowrap text-base font-normal leading-normal tracking-wide text-neutral-600">
                    {5} min read
                  </div>
                </div>
                <div className="size- flex items-center justify-start gap-1">
                  <CalendarIcon className="text-secondary-main size-5" />
                  <div className="font-roboto justify-start text-base font-normal leading-normal tracking-wide text-neutral-600">
                    {blogsData?.createdAt
                      ? new Date(blogsData.createdAt).toLocaleDateString()
                      : ""}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Main content area - container with row layout on lg */}
      <div className="container mx-auto w-full min-w-full">
        <div className="flex flex-col lg:flex-row lg:gap-[45px]">
          {/* Blog Content */}
          <div className="w-full">
            <div className="mb-8 w-full">
              <Image
                className="aspect-video w-full lg:min-w-[528px] xl:min-w-[820px] 2xl:min-w-[1198px]"
                src={blogsData?.imageUrl ?? ""}
                alt={blogsData?.imageUrl ?? ""}
                width={600}
                height={400}
              />
              <div className="prose mt-8 max-w-none">
                {blogsData?.content && (
                  <BlogPreviewComponent content={blogsData.content} />
                )}
              </div>
            </div>

            {/* Career section */}
          </div>

          {/* Table of Contents - sticky on lg screens */}
        </div>
      </div>
    </div>
  );
};
export default Blogs;
