//meta expo
import { Metada<PERSON> } from "next";
import { BlogsSection } from "@/components/landing-page/blogs-section";
import AnimatedElement from "@/components/shared/animated-element";
import ScrollToTop from "@/components/shared/scroll-to-top";
import TestimonialCard from "@/components/shared/testimonial-card";
import { api } from "@/trpc/server";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@acme/ui/components/ui/carousel";

export const metadata: Metadata = {
  title: "Blogs",
  description: "Blogs-FluidPe",
  openGraph: {
    title: "Blogs",
    description: "Unlock the Power of Your Mutual Funds",
    siteName: "Fluidpe",
  },
  icons: [{ rel: "icon", url: "/static/logos/day-logo.png" }],
};

const Blog = async () => {
  const blogs = await api.blog.getAllBlogs();
  const testimonials = await api.landingPage.getAllTestimonials();

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-grow pb-16 pt-24">
        <div className="container mx-auto px-4 md:px-6">
          <BlogsSection blogs={blogs} />

          {/* Testimonials Section */}
          <section className="mt-16 bg-gradient-to-b from-white to-fluidpe-light-teal/10 py-16">
            <div className="container relative z-10 mx-auto px-4">
              <AnimatedElement
                animation="fade-up"
                className="mb-12 text-center"
              >
                <h2 className="mb-4 text-3xl font-bold text-fluidpe-teal md:text-4xl">
                  What Our Clients Say
                </h2>
                <p className="mx-auto max-w-2xl text-gray-600">
                  Discover how our solutions have helped companies across
                  industries achieve their goals and transform their operations.
                </p>
              </AnimatedElement>

              <Carousel
                opts={{
                  align: "start",
                  loop: true,
                }}
                className="w-full"
              >
                <CarouselContent>
                  {testimonials.map((testimonial) => (
                    <CarouselItem key={testimonial.id} className="md:basis-1/4">
                      <div className="cursor-pointer">
                        <TestimonialCard testimonial={testimonial} />
                      </div>
                    </CarouselItem>
                  ))}
                </CarouselContent>
                <div className="mt-8 flex justify-center">
                  <CarouselPrevious className="static mr-2 translate-y-0" />
                  <CarouselNext className="static ml-2 translate-y-0" />
                </div>
              </Carousel>
            </div>
          </section>
        </div>
      </main>

      <ScrollToTop />
    </div>
  );
};

export default Blog;
