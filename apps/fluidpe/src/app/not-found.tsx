import React from "react";
import Link from "next/link";
import { Home } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";

const NotFound = () => {
  return (
    <div className="flex min-h-screen items-center justify-center bg-fluidpe-light-teal p-4">
      <div className="w-full max-w-md text-center">
        <h1 className="mb-4 text-8xl font-bold text-fluidpe-teal">404</h1>
        <h2 className="mb-6 text-2xl font-semibold text-fluidpe-teal">
          Page Not Found
        </h2>
        <p className="mb-8 text-gray-600">
          The page you're looking for doesn't exist or has been moved.
        </p>
        <Link href="/">
          <Button className="bg-fluidpe-teal text-white hover:bg-fluidpe-medium-teal">
            <Home className="mr-2 h-4 w-4" />
            Back to Home
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default NotFound;
