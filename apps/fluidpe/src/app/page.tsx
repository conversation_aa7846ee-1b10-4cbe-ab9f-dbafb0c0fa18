import { CTASection } from "@/components/landing-page/cta-section";
import { FAQSection } from "@/components/landing-page/faq-section";
import { FeaturesSection } from "@/components/landing-page/features-section";
import { HeroSection } from "@/components/landing-page/hero-section";
import { HowItWorksSection } from "@/components/landing-page/how-it-works-section";
import { LoanCalculator } from "@/components/landing-page/loan-calculator";
import { PartnersSection } from "@/components/landing-page/partners-section";
import { SubscribeToMail } from "@/components/landing-page/subscribe-mail";
import { TestimonialsSection } from "@/components/landing-page/testimonials-section";
import ScrollToTop from "@/components/shared/scroll-to-top";
import { api } from "@/trpc/server";

export default async function HomePage() {
  const testimonials = await api.landingPage.getAllTestimonials();
  const faqs = await api.landingPage.getAllFaqs();

  return (
    <div className="overflow-x-hidden font-sans">
      <HeroSection />
      <PartnersSection />
      <FeaturesSection />
      <HowItWorksSection />
      <LoanCalculator />
      <TestimonialsSection testimonials={testimonials} />
      <CTASection />
      <FAQSection faqs={faqs} />
      <ScrollToTop />
      <SubscribeToMail />
    </div>
  );
}
