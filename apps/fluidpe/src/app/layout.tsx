import type { Metadata, Viewport } from "next";

import "@acme/ui/globals.css";

import { Poppins } from "next/font/google";
import Footer from "@/components/shared/footer";
import Navbar from "@/components/shared/navbar";
import { TRPCReactProvider } from "@/trpc/react";

import { Toaster } from "@acme/ui/components/ui/sonner";

export const metadata: Metadata = {
  title: "Fluidpe",
  description: "Unlock the Power of Your Mutual Funds",
  openGraph: {
    title: "Fluidpe",
    description: "Unlock the Power of Your Mutual Funds",
    siteName: "Fluidpe",
  },
  icons: [{ rel: "icon", url: "/static/logos/day-logo.png" }],
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export default function RootLayout(props: { children: React.ReactNode }) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${poppins.className} bg-fluidpe-light-teal text-fluidpe-teal antialiased`}
      >
        <TRPCReactProvider>
          <Navbar />
          {props.children}
          <Footer />
          <Toaster richColors position="top-center" closeButton />
        </TRPCReactProvider>
      </body>
    </html>
  );
}
