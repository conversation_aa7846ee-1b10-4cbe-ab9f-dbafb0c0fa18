import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Privacy Policy",
  description: "Privacy Policy",
  openGraph: {
    title: "Privacy Policy",
    description: "Privacy Policy",
    siteName: "Fluidpe",
  },
  icons: [{ rel: "icon", url: "/static/logos/day-logo.png" }],
};

const PrivacyPolicyPage = () => {
  return <div>PrivacyPolicyPage</div>;
};

export default PrivacyPolicyPage;
