"use client";

import type { z } from "zod";
import AnimatedElement from "@/components/shared/animated-element";
import { TIME_SLOTS } from "@/lib/constants";
import { api } from "@/trpc/react";
import { zodResolver } from "@hookform/resolvers/zod";
import { format } from "date-fns";
import {
  Calendar as CalendarIcon,
  Check,
  Loader2,
  Mail,
  Phone,
  Send,
} from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { communicationMode, referralSource } from "@acme/db/schema";
import { Button } from "@acme/ui/components/ui/button";
import { Calendar } from "@acme/ui/components/ui/calendar";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@acme/ui/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@acme/ui/components/ui/select";
import { cn } from "@acme/ui/lib/utils";
import { TalkToAdvisorSchema } from "@acme/validators/fluidpe";

const TalkToAdvisorForm = () => {
  const form = useForm<z.infer<typeof TalkToAdvisorSchema>>({
    resolver: zodResolver(TalkToAdvisorSchema),
    defaultValues: {
      name: "",
      phone: "",
      email: "",
      city: "",
      state: "",
      otherReferralDetails: "",
      preferredTimeSlot: TIME_SLOTS[0],
    },
  });

  // Show "Other Details" field only when "OTHER" is selected as referral source
  const watchReferralSource = form.watch("referralSource");
  const showOtherReferralDetails =
    watchReferralSource === referralSource.enumValues[4];

  const submitForm = api.form.submitTalkToAdvisorForm.useMutation({
    onSuccess: (data) => {
      toast.success(data.message);
      form.reset();
    },
    onError: (opts) => {
      toast.error(opts.message);
    },
  });

  const onSubmit = (data: z.infer<typeof TalkToAdvisorSchema>) => {
    submitForm.mutate(data);
  };

  return (
    <AnimatedElement delay={10} animation="fade-in" className="w-full">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="w-full space-y-8 md:w-4/5 lg:w-2/3"
        >
          <div className="space-y-4">
            <h2 className="text-2xl font-bold text-fluidpe-teal">
              Personal Information
            </h2>

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {/* Name Field */}
              <FormField
                control={form.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Full Name</FormLabel>
                    <FormControl>
                      <Input
                        className="bg-white"
                        placeholder="Enter your full name"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Email Field */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Mail className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          className="bg-white pl-10"
                          placeholder="<EMAIL>"
                          type="email"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Phone Field */}
              <FormField
                control={form.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Phone className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          className="bg-white pl-10"
                          placeholder="Enter your phone number"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* State Field */}
              <FormField
                control={form.control}
                name="state"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>State</FormLabel>
                    <FormControl>
                      <Input
                        className="bg-white"
                        placeholder="Enter your state"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* City Field */}
              <FormField
                control={form.control}
                name="city"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>City</FormLabel>
                    <FormControl>
                      <Input
                        className="bg-white"
                        placeholder="Enter your city"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Communication Preferences Section */}
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-fluidpe-teal">
              Communication Preferences
            </h2>

            {/* Preferred Communication Mode */}
            <FormField
              control={form.control}
              name="preferredCommunication"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Preferred Communication Method</FormLabel>
                  <div className="flex flex-wrap gap-6">
                    {Object.values(communicationMode.enumValues).map((mode) => (
                      <Button
                        key={mode}
                        type="button"
                        variant={field.value === mode ? "default" : "outline"}
                        className={cn(
                          "min-w-[100px] flex-1",
                          field.value === mode && "bg-fluidpe-teal text-white",
                        )}
                        onClick={() => field.onChange(mode)}
                      >
                        {field.value === mode && (
                          <Check className="mr-2 h-4 w-4" />
                        )}
                        {mode === communicationMode.enumValues[0]
                          ? "Phone"
                          : mode === communicationMode.enumValues[1]
                            ? "Email"
                            : "WhatsApp"}
                      </Button>
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              {/* Preferred Date */}
              <FormField
                control={form.control}
                name="preferredDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col justify-between">
                    <FormLabel>Preferred Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn("w-full pl-3 text-left font-normal")}
                          >
                            <CalendarIcon className="mr-2 h-4 w-4" />
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Select a date</span>
                            )}
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                          disabled={(date) => date < new Date()}
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Preferred Time Slot */}
              <FormField
                control={form.control}
                name="preferredTimeSlot"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Preferred Time Slot</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="bg-white">
                          <SelectValue placeholder="Select a time slot" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {TIME_SLOTS.map((slot) => (
                          <SelectItem key={slot} value={slot}>
                            {slot}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>

          {/* Additional Information Section */}
          <div className="space-y-4">
            <h2 className="text-2xl font-bold text-fluidpe-teal">
              Additional Information
            </h2>

            {/* Referral Source */}
            <FormField
              control={form.control}
              name="referralSource"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>How did you hear about us?</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="bg-white">
                        <SelectValue placeholder="Select a referral source" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {referralSource.enumValues.map((source) => (
                        <SelectItem key={source} value={source}>
                          {source.replace(/_/g, " ")}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Other Referral Details (conditional) */}
            {showOtherReferralDetails && (
              <FormField
                control={form.control}
                name="otherReferralDetails"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Please specify other source</FormLabel>
                    <FormControl>
                      <Input
                        className="bg-white"
                        placeholder="Enter other referral details"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
          </div>

          <Button
            type="submit"
            className="w-full md:w-auto"
            disabled={submitForm.isPending}
          >
            {submitForm.isPending ? (
              <span className="flex items-center gap-2">
                <Loader2 className="animate-spin" /> Submitting...
              </span>
            ) : (
              <>
                <Send className="mr-2 h-4 w-4" />
                Submit Request
              </>
            )}
          </Button>
        </form>
      </Form>
    </AnimatedElement>
  );
};

export default TalkToAdvisorForm;
