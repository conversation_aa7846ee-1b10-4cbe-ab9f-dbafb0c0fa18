import { Metada<PERSON> } from "next";
import AnimatedElement from "@/components/shared/animated-element";

import TalkToAdvisorForm from "./talk-to-advisor-form";

export const metadata: Metadata = {
  title: "Talk to Advisor",
  description: "Talk to Advisor",
  openGraph: {
    title: "Talk to Advisor",
    description: "Talk to Advisor",
    siteName: "Fluidpe",
  },
  icons: [{ rel: "icon", url: "/static/logos/day-logo.png" }],
};

const TalkToAdvisorPage = () => {
  return (
    <main className="flex min-h-screen flex-col bg-white text-black">
      <div className="flex-grow pb-16 pt-24 xl:pt-32">
        <div className="container mx-auto px-4 md:px-6">
          <AnimatedElement>
            <h2 className="mb-6 bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal bg-clip-text text-start text-3xl font-bold text-transparent md:text-4xl">
              Connect With Our Financial Advisors
            </h2>
          </AnimatedElement>

          <TalkToAdvisorForm />
        </div>
      </div>
    </main>
  );
};

export default TalkToAdvisorPage;
