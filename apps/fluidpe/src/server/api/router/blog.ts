import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { blog } from "@acme/db/schema";

import { createTRPCRouter, publicProcedure } from "../trpc";

const blogRouter = createTRPCRouter({
  getAllBlogs: publicProcedure.query(async ({ ctx }) => {
    try {
      const blogs = await ctx.db.query.blog.findMany({
        where: eq(blog.published, true),
      });

      return blogs;
    } catch (err) {
      if (err instanceof TRPCError) {
        throw err;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "An error occurred while fetching blogs",
        cause: err,
      });
    }
  }),

  getBlogByIdOrSlug: publicProcedure
    .input(z.object({ id: z.string().optional(), slug: z.string().optional() }))
    .query(async ({ ctx, input }) => {
      const blogId = input.id;
      const blogSlug = input.slug;

      if (!blogId && !blogSlug) {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Either id or slug must be provided",
        });
      }

      try {
        const blogData = await ctx.db.query.blog.findFirst({
          where: eq(blog.slug, blogSlug ?? ""),
        });

        return blogData;
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "An error occurred while fetching blog",
          cause: err,
        });
      }
    }),
});

export default blogRouter;
