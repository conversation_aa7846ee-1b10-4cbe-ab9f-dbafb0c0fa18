import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { eq } from "@acme/db";
import { mailSubscription } from "@acme/db/schema";

import { createTRPCRouter, publicProcedure } from "../trpc";

const landingPageRouter = createTRPCRouter({
  getAllTestimonials: publicProcedure.query(async ({ ctx }) => {
    try {
      const testimonials = await ctx.db.query.testimonial.findMany({});

      return testimonials;
    } catch (err) {
      if (err instanceof TRPCError) {
        throw err;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "An error occurred while fetching testimonials",
        cause: err,
      });
    }
  }),

  getAllFaqs: publicProcedure.query(async ({ ctx }) => {
    try {
      const faqs = await ctx.db.query.faq.findMany({});

      return faqs;
    } catch (err) {
      if (err instanceof TRPCError) {
        throw err;
      }

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: "An error occurred while fetching FAQs",
        cause: err,
      });
    }
  }),
  subscribeToMail: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { email } = input;

      try {
        // Check if this email is already subscribed
        const existingSubscription =
          await ctx.db.query.mailSubscription.findFirst({
            where: eq(mailSubscription.customerEmail, email),
          });

        if (existingSubscription) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "This email is already subscribed",
          });
        }

        // Create a subscription for the email
        await ctx.db.insert(mailSubscription).values({
          customerEmail: email,
        });

        return {
          success: true,
          message: "Successfully subscribed to the newsletter",
        };
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "An error occurred while subscribing to the newsletter",
          cause: err,
        });
      }
    }),

  unsubscribeFromMail: publicProcedure
    .input(
      z.object({
        email: z.string().email(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      const { email } = input;

      try {
        const subscription = await ctx.db.query.mailSubscription.findFirst({
          where: eq(mailSubscription.customerEmail, email),
        });

        if (!subscription) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "No subscription found for this email",
          });
        }

        // Delete the subscription
        await ctx.db
          .delete(mailSubscription)
          .where(eq(mailSubscription.customerEmail, email));

        return {
          success: true,
          message: "Successfully unsubscribed from the newsletter",
        };
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "An error occurred while unsubscribing from the newsletter",
          cause: err,
        });
      }
    }),
});

export default landingPageRouter;
