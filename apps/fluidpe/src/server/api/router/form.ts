import { TRPCError } from "@trpc/server";

import { contactRequest } from "@acme/db/schema";
import { TalkToAdvisorSchema } from "@acme/validators/fluidpe";

import { createTRPCRouter, publicProcedure } from "../trpc";

const formsRouter = createTRPCRouter({
  submitTalkToAdvisorForm: publicProcedure
    .input(TalkToAdvisorSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        await ctx.db.insert(contactRequest).values({
          name: input.name,
          phone: input.phone,
          updatedAt: new Date().toISOString(),
          email: input.email,
          preferredCommunication: input.preferredCommunication,
          preferredDate: input.preferredDate.toISOString(),
          preferredTimeSlot: input.preferredTimeSlot,
          city: input.city,
          state: input.state,
          referralSource: input.referralSource,
          otherReferralDetails: input.otherReferralDetails,
        });

        return {
          message:
            "Thanks for submitting the form, Our financial advisor will contact you shortly.",
        };
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "An error occurred while submitting the form",
          cause: err,
        });
      }
    }),
});

export default formsRouter;
