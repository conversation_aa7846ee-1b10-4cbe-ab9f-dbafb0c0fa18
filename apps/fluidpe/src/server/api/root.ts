import { createCallerFactory, createTRPCRouter } from "@/server/api/trpc";

import blogRouter from "./router/blog";
import formsRouter from "./router/form";
import landingPageRouter from "./router/landing-page";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  blog: blogRouter,
  landingPage: landingPageRouter,
  form: formsRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
