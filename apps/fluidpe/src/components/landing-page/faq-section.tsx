import AnimatedElement from "@/components/shared/animated-element";
import FaqItem from "@/components/shared/faq-item";

import type { faq } from "@acme/db/schema";

export function FAQSection({ faqs }: { faqs: (typeof faq.$inferSelect)[] }) {
  if (faqs.length === 0) return null;

  return (
    <section id="faq" className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <AnimatedElement>
          <h2 className="mb-6 bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal bg-clip-text text-center text-3xl font-bold text-transparent md:text-4xl">
            Frequently Asked Questions
          </h2>
        </AnimatedElement>

        {/* <AnimatedElement delay={200}>
          <p className="mx-auto mb-12 max-w-3xl text-center text-lg text-gray-600 md:text-xl">
            Find answers to common questions about loans against mutual funds
          </p>
        </AnimatedElement> */}

        <div className="mx-auto mt-12 max-w-3xl">
          {faqs.map((item, index) => (
            <FaqItem
              key={index}
              question={item.question}
              answer={item.answer}
              delay={index * 100}
            />
          ))}
        </div>
      </div>
    </section>
  );
}
