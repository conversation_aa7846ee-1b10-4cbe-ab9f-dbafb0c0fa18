import Link from "next/link";
import AnimatedElement from "@/components/shared/animated-element";
import { env } from "@/env";

import { Button } from "@acme/ui/components/ui/button";

export function CTASection() {
  return (
    <section className="bg-cta-pattern py-16 md:py-24">
      <div className="container mx-auto px-4">
        <div className="mx-auto max-w-3xl text-center">
          <AnimatedElement>
            <h2 className="mb-6 text-3xl font-bold text-fluidpe-teal md:text-4xl">
              Ready to Unlock the Value of Your Mutual Fund Portfolio?
            </h2>
          </AnimatedElement>

          <AnimatedElement delay={200}>
            <p className="mb-8 text-lg text-gray-600">
              Apply now to get a personalized quote and access funds without
              selling your mutual fund investments. Our experts are ready to
              help you every step of the way.
            </p>
          </AnimatedElement>

          <AnimatedElement delay={400}>
            <div className="flex flex-col justify-center gap-4 sm:flex-row">
              <Button
                className="rounded-lg border-none bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal px-6 py-3 font-medium text-white transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
                size="lg"
                asChild
              >
                <Link
                  href={env.NEXT_PUBLIC_CUSTOMER_WEBAPP_URL}
                  target="_blank"
                >
                  Apply Now
                </Link>
              </Button>
              {/* <Button
                className="rounded-lg border border-fluidpe-teal/30 bg-white px-6 py-3 font-medium text-fluidpe-teal shadow-sm transition-all duration-300 hover:-translate-y-1 hover:border-fluidpe-teal hover:bg-fluidpe-light-teal hover:shadow-md"
                size="lg"
                asChild
              >
                <Link href="/talk-to-advisor">Talk to an Advisor</Link>
              </Button> */}
            </div>
          </AnimatedElement>
        </div>
      </div>
    </section>
  );
}
