"use client";

import AnimatedElement from "@/components/shared/animated-element";
import TestimonialCard from "@/components/shared/testimonial-card";

import { testimonial } from "@acme/db/schema";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@acme/ui/components/ui/carousel";

export function TestimonialsSection({
  testimonials,
}: {
  testimonials: (typeof testimonial.$inferSelect)[];
}) {
  if (testimonials.length === 0) return null;

  return (
    <section
      id="testimonials"
      className="relative overflow-hidden bg-fluidpe-light-teal py-16 md:py-24"
    >
      <div className="absolute inset-0 z-0">
        <div className="absolute left-[-10%] top-[-5%] h-96 w-96 rounded-full bg-white opacity-60 blur-3xl"></div>
        <div className="absolute bottom-[10%] right-[-5%] h-80 w-80 rounded-full bg-fluidpe-light-gold opacity-40 blur-3xl"></div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <AnimatedElement>
          <h2 className="mb-6 bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal bg-clip-text text-center text-3xl font-bold text-transparent md:text-4xl">
            What Our Customers Say
          </h2>
        </AnimatedElement>

        <AnimatedElement delay={200}>
          <p className="mx-auto mb-12 max-w-3xl text-center text-lg text-gray-600 md:text-xl">
            Join thousands of satisfied customers who've experienced the Fluidpe
            advantage
          </p>
        </AnimatedElement>

        <div className="mx-auto mt-12 max-w-5xl">
          <Carousel className="w-full">
            <CarouselContent>
              {testimonials.map((testimonial) => (
                <CarouselItem key={testimonial.id} className="md:basis-1/1">
                  <TestimonialCard testimonial={testimonial} />
                </CarouselItem>
              ))}
            </CarouselContent>
            <div className="mt-8 flex justify-center">
              <CarouselPrevious className="static relative left-0 mr-4 translate-y-0" />
              <CarouselNext className="static relative right-0 translate-y-0" />
            </div>
          </Carousel>
        </div>
      </div>
    </section>
  );
}
