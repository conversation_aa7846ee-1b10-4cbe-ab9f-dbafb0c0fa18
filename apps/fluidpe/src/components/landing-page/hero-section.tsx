"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import AnimatedElement from "@/components/shared/animated-element";
import { env } from "@/env";
import { ArrowRight, Percent, TrendingUp, Zap } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

export function HeroSection() {
  const [hoverEffect, setHoverEffect] = useState(false);
  const [animationComplete, setAnimationComplete] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimationComplete(true);
    }, 2000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <section className="relative bg-hero-pattern pb-16 pt-28 md:pb-24 md:pt-32">
      <div className="absolute inset-0 z-0 overflow-hidden">
        <div className="absolute right-[-10%] top-[10%] h-72 w-72 animate-float rounded-full bg-fluidpe-light-teal opacity-60 blur-3xl"></div>
        <div
          className="absolute bottom-[5%] left-[-5%] h-80 w-80 animate-float rounded-full bg-fluidpe-light-gold opacity-40 blur-3xl"
          style={{
            animationDelay: "2s",
          }}
        ></div>

        <div
          className={`absolute left-[20%] top-[25%] h-32 w-32 rounded-full bg-blue-200/20 opacity-30 blur-xl transition-all duration-1000 ${hoverEffect ? "scale-150" : "scale-100"}`}
        ></div>
        <div
          className={`absolute bottom-[20%] right-[25%] h-40 w-40 rounded-full bg-green-200/20 opacity-30 blur-xl transition-all delay-300 duration-1000 ${hoverEffect ? "translate-x-10 scale-125" : "scale-100"}`}
        ></div>
      </div>

      <div
        className="container relative z-10 mx-auto px-4 transition-all duration-700"
        onMouseEnter={() => setHoverEffect(true)}
        onMouseLeave={() => setHoverEffect(false)}
      >
        <div className="grid grid-cols-1 items-center gap-12 lg:grid-cols-2">
          <div className="text-left">
            <AnimatedElement>
              <span className="mb-4 inline-block rounded-full bg-gradient-to-r from-fluidpe-light-teal to-fluidpe-medium-teal/30 px-3 py-1 text-sm font-medium text-fluidpe-teal transition-all duration-300 hover:from-fluidpe-teal/20 hover:to-fluidpe-medium-teal/50 hover:shadow-lg">
                <span className="inline-flex items-center">
                  <Zap className="mr-1 h-3.5 w-3.5 animate-pulse" />
                  Financial Freedom At Your Fingertips
                </span>
              </span>
            </AnimatedElement>

            <AnimatedElement delay={200}>
              <h1 className="mb-6 bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal bg-clip-text text-4xl font-bold leading-tight text-transparent md:text-5xl lg:text-6xl">
                Unlock the Power of Your Mutual Funds
              </h1>
            </AnimatedElement>

            <AnimatedElement delay={400}>
              <p className="relative mb-8 max-w-lg text-lg text-gray-600 md:text-xl">
                Get instant loans against your mutual fund investments without
                selling them. Enjoy competitive interest rates starting at{" "}
                <span className="group relative inline-block font-semibold text-fluidpe-teal">
                  8.5%
                  <span className="absolute -bottom-1 left-0 h-0.5 w-full origin-left scale-x-0 transform bg-fluidpe-teal transition-transform duration-300 group-hover:scale-x-100"></span>
                </span>{" "}
                with minimal documentation.
              </p>
            </AnimatedElement>

            <AnimatedElement delay={600}>
              <Link
                href={env.NEXT_PUBLIC_CUSTOMER_WEBAPP_URL}
                target="_blank"
                className="flex flex-wrap gap-4"
              >
                <Button
                  className="group relative overflow-hidden rounded-lg border-none bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal px-6 py-3 font-medium text-white transition-all duration-300 hover:-translate-y-1 hover:shadow-lg"
                  size="lg"
                >
                  <span className="relative z-10 flex items-center">
                    Talk to an advisor{" "}
                    <ArrowRight className="ml-2 h-4 w-4 transform transition-transform duration-300 group-hover:translate-x-1" />
                  </span>
                  <span className="absolute inset-0 h-full w-full bg-gradient-to-r from-fluidpe-medium-teal to-fluidpe-teal opacity-0 transition-opacity duration-300 group-hover:opacity-100"></span>
                </Button>
              </Link>
            </AnimatedElement>

            <AnimatedElement delay={800}>
              <div className="mt-8 grid grid-cols-3 gap-4">
                <div className="group cursor-pointer rounded-lg border border-white/50 bg-white/30 p-3 text-center shadow-sm backdrop-blur-sm transition-all duration-300 hover:bg-white/50 hover:shadow-md">
                  <p className="text-2xl font-bold text-fluidpe-teal transition-transform duration-300 group-hover:scale-110 md:text-3xl">
                    ₹2.5Cr+
                  </p>
                  <p className="text-sm text-gray-600 transition-colors duration-300 group-hover:text-fluidpe-teal/70">
                    Loans Disbursed
                  </p>
                </div>
                <div className="group cursor-pointer rounded-lg border border-white/50 bg-white/30 p-3 text-center shadow-sm backdrop-blur-sm transition-all duration-300 hover:bg-white/50 hover:shadow-md">
                  <p className="text-2xl font-bold text-fluidpe-teal transition-transform duration-300 group-hover:scale-110 md:text-3xl">
                    500+
                  </p>
                  <p className="text-sm text-gray-600 transition-colors duration-300 group-hover:text-fluidpe-teal/70">
                    Happy Customers
                  </p>
                </div>
                <div className="group cursor-pointer rounded-lg border border-white/50 bg-white/30 p-3 text-center shadow-sm backdrop-blur-sm transition-all duration-300 hover:bg-white/50 hover:shadow-md">
                  <p className="text-2xl font-bold text-fluidpe-teal transition-transform duration-300 group-hover:scale-110 md:text-3xl">
                    *8.5%
                  </p>
                  <p className="text-sm text-gray-600 transition-colors duration-300 group-hover:text-fluidpe-teal/70">
                    Interest Rate
                  </p>
                </div>
              </div>
            </AnimatedElement>
          </div>

          <AnimatedElement delay={300} className="relative">
            <div
              className={`relative z-10 transform transition-all duration-700 ${hoverEffect ? "scale-105" : "scale-100"}`}
            >
              <div className="group relative overflow-hidden rounded-2xl shadow-xl">
                <div className="absolute h-full w-full bg-gradient-to-br from-fluidpe-teal to-fluidpe-medium-teal opacity-90 transition-opacity duration-500 group-hover:opacity-75"></div>
                <img
                  src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&q=85&fm=jpg&crop=entropy&cs=srgb&w=1200"
                  alt="Trading Dashboard"
                  className="h-auto w-full mix-blend-overlay transition-transform duration-700 group-hover:scale-110"
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="p-6 text-center text-white">
                    <h3 className="mb-2 text-2xl font-bold transition-colors duration-300 group-hover:text-fluidpe-light-gold">
                      Loan Against Mutual Funds
                    </h3>
                    <p className="mb-4">Unlock liquidity from your portfolio</p>
                    <div className="inline-block transform rounded-lg bg-white/20 p-4 backdrop-blur-sm transition-all duration-300 hover:shadow-lg group-hover:-translate-y-1 group-hover:bg-white/30">
                      <p className="mb-1 text-sm text-white/90">Loan Amount</p>
                      <p className="text-3xl font-bold">₹25,00,000</p>
                    </div>
                  </div>
                </div>
                <div className="absolute inset-0 bg-black/0 transition-all duration-500 group-hover:bg-black/10"></div>
              </div>

              <div className="absolute -bottom-6 -right-6 rounded-lg bg-white p-4 shadow-lg hover:bg-fluidpe-light-teal/30 hover:shadow-xl">
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-green-100 p-2 transition-colors duration-300 group-hover:bg-green-200">
                    <TrendingUp className="h-5 w-5 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Portfolio Value</p>
                    <p className="font-semibold text-fluidpe-teal">₹1.2 Cr</p>
                  </div>
                </div>
              </div>

              <div className="absolute -left-6 -top-6 rounded-lg bg-white p-4 shadow-lg hover:bg-fluidpe-light-teal/30 hover:shadow-xl">
                <div className="flex items-center gap-3">
                  <div className="rounded-full bg-blue-100 p-2 transition-colors duration-300 group-hover:bg-blue-200">
                    <Percent className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">Interest Rate</p>
                    <p className="font-semibold text-fluidpe-teal">8.5% p.a</p>
                  </div>
                </div>
              </div>

              <div
                className={`absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform transition-all duration-700 ${animationComplete ? "opacity-0" : "opacity-100"}`}
              >
                <div className="animate-pulse rounded-full bg-white/80 p-6 shadow-xl backdrop-blur-sm">
                  <Zap className="h-10 w-10 text-fluidpe-teal" />
                </div>
              </div>
            </div>
          </AnimatedElement>
        </div>
      </div>
    </section>
  );
}
