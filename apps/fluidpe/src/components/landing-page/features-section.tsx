import AnimatedElement from "@/components/shared/animated-element";
import FeatureCard from "@/components/shared/feature-card";
import {
  Award,
  Briefcase,
  Clock,
  LineChart,
  Shield,
  Wallet,
} from "lucide-react";

export function FeaturesSection() {
  return (
    <section
      id="features"
      className="relative overflow-hidden bg-gradient-to-b from-white to-fluidpe-light-teal/10 py-16 md:py-24"
    >
      <div className="pointer-events-none absolute inset-0 z-0 overflow-hidden">
        <div className="absolute -right-[10%] top-[20%] h-[600px] w-[600px] animate-float rounded-full bg-fluidpe-light-teal/20 opacity-60 blur-3xl"></div>
        <div
          className="absolute -left-[5%] bottom-[10%] h-[500px] w-[500px] rounded-full bg-fluidpe-light-gold/20 opacity-50 blur-3xl"
          style={{
            animationDelay: "1.5s",
          }}
        ></div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <AnimatedElement animation="fade-up">
          <div className="mb-16 flex flex-col items-center justify-center">
            <div className="mb-4 rounded-full bg-gradient-to-r from-fluidpe-teal/20 to-fluidpe-medium-teal/20 p-2 px-4">
              <span className="text-sm font-semibold text-fluidpe-teal">
                Why our clients trust us
              </span>
            </div>
            <h2 className="mb-6 bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal bg-clip-text text-center text-3xl font-bold text-transparent md:text-4xl">
              Why Choose Fluidpe
            </h2>
            <div className="mb-6 h-1 w-20 rounded-full bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal"></div>
            <p className="mx-auto mb-6 max-w-3xl text-center text-lg text-gray-600 md:text-xl">
              Unlock the full potential of your mutual fund investments with our
              innovative loan solutions
            </p>
          </div>
        </AnimatedElement>

        <div className="mt-2 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          <FeatureCard
            icon={Wallet}
            title="Competitive Interest Rates"
            description="Enjoy interest rates starting from just 8.5% p.a., significantly lower than personal loans and credit cards."
            delay={0}
          />

          <FeatureCard
            icon={Clock}
            title="Quick Disbursals"
            description="Get funds in your account within 24 hours after approval, with minimal documentation requirements."
            delay={100}
            animation="fade-down"
          />

          <FeatureCard
            icon={LineChart}
            title="Retain Market Upside"
            description="Continue to benefit from potential market appreciation while accessing funds for your immediate needs."
            delay={200}
            animation="fade-up"
          />

          <FeatureCard
            icon={Shield}
            title="Flexible Repayment Options"
            description="Choose from multiple repayment plans that suit your cash flow, with no prepayment penalties."
            delay={300}
            animation="fade-right"
          />

          <FeatureCard
            icon={Award}
            title="Higher Loan Amounts"
            description="Access up to 80% of your mutual fund portfolio value, with loan amounts ranging from ₹1 Lakh to ₹5 Crores."
            delay={400}
            animation="fade-up"
          />

          <FeatureCard
            icon={Briefcase}
            title="Wide Range of Acceptable Funds"
            description="Pledge various mutual fund schemes including equity, debt, and hybrid funds to secure your loan with maximum flexibility."
            delay={500}
            animation="fade-left"
          />
        </div>

        {/* <AnimatedElement
          animation="fade-up"
          delay={600}
          className="mt-16 text-center"
        >
          <div className="inline-block rounded-xl border border-fluidpe-light-teal/30 bg-white px-6 py-4 shadow-md transition-all duration-300 hover:shadow-lg">
            <div className="flex items-center justify-center gap-2 font-medium text-fluidpe-teal">
              <Check className="h-5 w-5" />
              <span>No hidden charges</span>
              <div className="h-1 w-1 rounded-full bg-fluidpe-teal/30"></div>
              <Check className="h-5 w-5" />
              <span>Transparent process</span>
              <div className="h-1 w-1 rounded-full bg-fluidpe-teal/30"></div>
              <Check className="h-5 w-5" />
              <span>Excellent support</span>
            </div>
          </div>
        </AnimatedElement> */}
      </div>
    </section>
  );
}
