import AnimatedElement from "@/components/shared/animated-element";
import ProcessStep from "@/components/shared/process-step";
import { CloudUpload, FileText, Send, Wallet } from "lucide-react";

export function HowItWorksSection() {
  return (
    <section
      id="how-it-works"
      className="relative overflow-hidden bg-gradient-to-b from-white to-fluidpe-light-teal/20 py-20"
    >
      <div className="pointer-events-none absolute inset-0 z-0">
        <div className="absolute bottom-[-5%] right-[-10%] h-96 w-96 rounded-full bg-fluidpe-light-teal opacity-40 blur-3xl"></div>
        <div className="absolute left-[-5%] top-[10%] h-80 w-80 rounded-full bg-fluidpe-light-gold opacity-30 blur-3xl"></div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <AnimatedElement animation="fade-up">
          <h2 className="mb-6 bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal bg-clip-text text-center text-3xl font-bold text-transparent md:text-4xl">
            How It Works
          </h2>
        </AnimatedElement>

        <AnimatedElement delay={200} animation="fade-up">
          <p className="mx-auto mb-16 max-w-3xl text-center text-lg text-gray-600 md:text-xl">
            Four simple steps to unlock the value of your mutual fund
            investments
          </p>
        </AnimatedElement>

        <div className="mt-10 grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          <ProcessStep
            number={1}
            title="Upload Documents"
            description="Upload KYC, PAN and other documents for quick verification."
            delay={0}
            icon={<CloudUpload className="h-10 w-10" />}
          />

          <ProcessStep
            number={2}
            title="Select Mutual Funds"
            description="Choose the mutual funds you wish to pledge as collateral."
            delay={200}
            icon={<FileText className="h-10 w-10" />}
          />

          <ProcessStep
            number={3}
            title="Digital Signing"
            description="Complete the digital signing process securely online - no paperwork."
            delay={400}
            icon={<Send className="h-10 w-10" />}
          />

          <ProcessStep
            number={4}
            title="Instant Disbursal"
            description="Receive funds instantly in your bank account once approved."
            delay={600}
            icon={<Wallet className="h-10 w-10" />}
          />
        </div>
      </div>
    </section>
  );
}
