"use client";

import { useState } from "react";
import { api } from "@/trpc/react";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import { Input } from "@acme/ui/components/ui/input";

export function SubscribeToMail() {
  const [userEmail, setUserEmail] = useState<string>("");
  const subscribeMutation = api.landingPage.subscribeToMail.useMutation({
    onSuccess: ({ success, message }) => {
      if (success) {
        toast.success(message);
        setUserEmail("");
      }
    },
    onError: ({ message }) => {
      toast.error(message);
    },
  });
  return (
    <section
      id="subscribe-to-mail"
      className="relative overflow-hidden bg-gradient-to-b from-white to-fluidpe-light-teal/20 py-20"
    >
      <div className="pointer-events-none absolute inset-0 z-0">
        <div className="absolute bottom-[-5%] right-[-10%] h-96 w-96 rounded-full bg-fluidpe-light-teal opacity-40 blur-3xl"></div>
        <div className="absolute left-[-5%] top-[10%] h-80 w-80 rounded-full bg-fluidpe-light-gold opacity-30 blur-3xl"></div>
      </div>
      <div className="flex w-full flex-col items-center justify-center gap-3">
        <h2 className="text-4xl font-bold">Subscribe to our newsletter</h2>
        <p className="text-lg">Get the latest news and updates from our team</p>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            subscribeMutation.mutate({ email: userEmail });
          }}
          className="flex flex-row items-center justify-center gap-4"
        >
          <Input
            type="email"
            placeholder="Enter your email"
            value={userEmail}
            className="min-w-[300px]"
            onChange={(e) => setUserEmail(e.target.value)}
          />
          <Button type="submit">Subscribe</Button>
        </form>
      </div>
    </section>
  );
}
