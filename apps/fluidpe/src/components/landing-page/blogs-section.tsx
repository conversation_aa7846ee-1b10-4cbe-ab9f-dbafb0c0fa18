import AnimatedElement from "@/components/shared/animated-element";
import BlogCard from "@/components/shared/blog-card";

import type { blog } from "@acme/db/schema";

export function BlogsSection({
  blogs,
}: {
  blogs: (typeof blog.$inferSelect)[];
}) {
  return (
    <section id="blogs" className="py-16 md:py-24">
      <div className="container mx-auto px-4">
        <AnimatedElement>
          <h2 className="mb-6 bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal bg-clip-text text-center text-3xl font-bold text-transparent md:text-4xl">
            Financial Insights & Resources
          </h2>
        </AnimatedElement>

        <AnimatedElement delay={200}>
          <p className="mx-auto mb-12 max-w-3xl text-center text-lg text-gray-600 md:text-xl">
            Stay informed with our latest articles on mutual funds, investments,
            and financial planning
          </p>
        </AnimatedElement>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {blogs.map((post) => (
            <BlogCard key={post.id} blog={post} />
          ))}
        </div>

        {/* <AnimatedElement delay={400} className="mt-12 text-center">
          <Button
            className="group rounded-lg border border-fluidpe-teal/30 bg-white px-6 py-3 font-medium text-fluidpe-teal transition-all duration-300 hover:border-fluidpe-teal hover:bg-fluidpe-light-teal hover:shadow-md"
            asChild
          >
            <Link href="/blogs">
              View All Articles
              <ArrowRight className="ml-2 h-4 w-4 transition-transform duration-300 group-hover:translate-x-1" />
            </Link>
          </Button>
        </AnimatedElement> */}
      </div>
    </section>
  );
}
