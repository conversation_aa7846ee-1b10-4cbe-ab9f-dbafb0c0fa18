import Image from "next/image";
import AnimatedElement from "@/components/shared/animated-element";

export function PartnersSection() {
  return (
    <section className="bg-fluidpe-light-gray py-10">
      <div className="container mx-auto px-4">
        <AnimatedElement>
          <p className="mb-8 text-center text-gray-500">
            Trusted by leading institutions
          </p>
        </AnimatedElement>

        <div className="flex items-center justify-center gap-8 md:gap-16">
          <AnimatedElement delay={0} animation="fade-up">
            <div className="relative aspect-[110/47] w-[110px] cursor-pointer grayscale hover:grayscale-0 md:aspect-[131/56] md:w-[131px] lg:aspect-[136/58] lg:w-[136px] xl:aspect-[176/75] xl:w-[176px]">
              <Image
                src="/static/images/cams-logo.svg"
                alt="CAMS"
                fill
                className="relative object-contain mix-blend-multiply"
              />
            </div>
          </AnimatedElement>
          <AnimatedElement delay={100} animation="fade-up">
            <div className="aspect-[110/47] w-[110px] cursor-pointer grayscale hover:grayscale-0 md:aspect-[131/56] md:w-[131px] lg:aspect-[136/58] lg:w-[136px] xl:aspect-[176/75] xl:w-[176px]">
              <Image
                src="/static/images/kfintech-logo.png"
                alt="KFINTECH"
                fill
                className="relative object-contain mix-blend-multiply"
              />
            </div>
          </AnimatedElement>
          <AnimatedElement delay={200} animation="fade-up">
            <div className="aspect-[110/47] w-[110px] cursor-pointer grayscale hover:grayscale-0 md:aspect-[131/56] md:w-[131px] lg:aspect-[136/58] lg:w-[136px] xl:aspect-[176/75] xl:w-[176px]">
              <Image
                src="/static/images/nsdl-logo.png"
                alt="NSDL"
                fill
                className="relative object-cover mix-blend-multiply"
              />
            </div>
          </AnimatedElement>
        </div>
      </div>
    </section>
  );
}
