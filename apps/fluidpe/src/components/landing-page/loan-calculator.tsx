"use client";

import { useState } from "react";
import AnimatedElement from "@/components/shared/animated-element";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Award,
  CheckCircle,
  Clock,
  CreditCard,
  Percent,
  Star,
  TrendingDown,
  TrendingUp,
  Wallet,
} from "lucide-react";

import { Slider } from "@acme/ui/components/ui/slider";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";

export function LoanCalculator() {
  const [loanAmount, setLoanAmount] = useState(500000); // ₹5 Lakh default
  const [loanDuration, setLoanDuration] = useState(24); // 24 months
  const [interestRate, setInterestRate] = useState(8.5); // 8.5% default

  const calculateEMI = () => {
    const principal = loanAmount;
    const ratePerMonth = interestRate / 1200; // Monthly interest rate
    const tenure = loanDuration; // in months

    const emi =
      (principal * ratePerMonth * Math.pow(1 + ratePerMonth, tenure)) /
      (Math.pow(1 + ratePerMonth, tenure) - 1);
    return Math.round(emi);
  };

  const emi = calculateEMI();

  const calculateSavings = () => {
    const personalLoanRate = 14; // 14% for personal loan
    const creditCardRate = 36; // 36% for credit card

    const ourInterest = loanAmount * (interestRate / 100) * (loanDuration / 12);
    const personalLoanInterest =
      loanAmount * (personalLoanRate / 100) * (loanDuration / 12);
    const creditCardInterest =
      loanAmount * (creditCardRate / 100) * (loanDuration / 12);
    return {
      personalLoan: Math.round(personalLoanInterest - ourInterest),
      creditCard: Math.round(creditCardInterest - ourInterest),
      personalLoanExtra: Math.round(personalLoanInterest),
      creditCardExtra: Math.round(creditCardInterest),
      ourInterest: Math.round(ourInterest),
    };
  };

  const savings = calculateSavings();

  return (
    <section
      id="benefits"
      className="relative overflow-hidden bg-white py-16 md:py-24"
    >
      <div className="pointer-events-none absolute inset-0 z-0">
        <div className="absolute right-[-5%] top-[10%] h-72 w-72 rounded-full bg-fluidpe-light-teal/30 blur-3xl"></div>
        <div className="absolute bottom-[10%] left-[-5%] h-80 w-80 rounded-full bg-fluidpe-light-gold/30 blur-3xl"></div>
      </div>

      <div className="container relative z-10 mx-auto px-4">
        <AnimatedElement>
          <h2 className="gradient-text-animate mb-6 text-center text-3xl font-bold md:text-4xl">
            Calculate Your Savings with Loan Against Mutual Funds
          </h2>
        </AnimatedElement>

        <AnimatedElement delay={200}>
          <p className="mx-auto mb-8 max-w-3xl text-center text-lg text-gray-600 md:text-xl">
            See how much you can save compared to traditional financing options
          </p>
        </AnimatedElement>

        <div className="mx-auto mt-12 max-w-6xl">
          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <AnimatedElement delay={100} animation="fade-up" className="h-full">
              <div className="premium-card h-full overflow-hidden">
                <div className="bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal px-6 py-4">
                  <h3 className="flex items-center text-xl font-semibold text-white">
                    <Wallet className="mr-2 h-5 w-5" /> Loan Calculator
                  </h3>
                  <p className="text-sm text-white/80">
                    Adjust the sliders to see your potential savings
                  </p>
                </div>

                <div className="p-6">
                  <div className="space-y-6">
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <label className="flex items-center text-sm font-medium text-gray-700">
                          <TrendingUp className="mr-1 h-4 w-4 text-fluidpe-teal" />{" "}
                          Loan Amount
                        </label>
                        <span className="text-sm font-medium text-fluidpe-teal">
                          ₹{loanAmount.toLocaleString()}
                        </span>
                      </div>
                      <Slider
                        min={100000}
                        max={5000000}
                        step={100000}
                        value={[loanAmount]}
                        onValueChange={(value) => setLoanAmount(value[0]!)}
                        className="calculator-slider cursor-pointer"
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <label className="flex items-center text-sm font-medium text-gray-700">
                          <Clock className="mr-1 h-4 w-4 text-fluidpe-teal" />{" "}
                          Duration (Months)
                        </label>
                        <span className="text-sm font-medium text-fluidpe-teal">
                          {loanDuration} months
                        </span>
                      </div>
                      <Slider
                        min={6}
                        max={60}
                        step={6}
                        value={[loanDuration]}
                        onValueChange={(value) => setLoanDuration(value[0]!)}
                        className="calculator-slider cursor-pointer"
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <label className="flex items-center text-sm font-medium text-gray-700">
                          <Percent className="mr-1 h-4 w-4 text-fluidpe-teal" />{" "}
                          Interest Rate
                        </label>
                        <span className="text-sm font-medium text-fluidpe-teal">
                          {interestRate}%
                        </span>
                      </div>
                      <Slider
                        min={8.5}
                        max={12.99}
                        step={0.5}
                        value={[interestRate]}
                        onValueChange={(value) => setInterestRate(value[0]!)}
                        className="calculator-slider cursor-pointer"
                      />
                    </div>
                  </div>

                  <div className="mt-8 rounded-xl bg-gradient-to-br from-fluidpe-light-teal/50 to-fluidpe-light-gold/30 p-6 shadow-inner">
                    <div className="flex items-center justify-between">
                      <h4 className="flex items-center text-lg font-semibold text-fluidpe-teal">
                        Your Loan Summary
                      </h4>
                      <div className="animate-pulse-soft rounded-full bg-fluidpe-teal px-3 py-1 text-xs font-semibold text-white">
                        Best Rate!
                      </div>
                    </div>

                    <div className="mt-4 space-y-4">
                      <div className="flex items-center justify-between border-b border-fluidpe-light-teal/30 pb-3">
                        <span className="font-medium text-gray-700">
                          Monthly EMI
                        </span>
                        <span className="animate-float-slow bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal bg-clip-text text-2xl font-bold text-transparent">
                          ₹{calculateEMI().toLocaleString()}
                        </span>
                      </div>

                      <div className="flex items-center justify-between border-b border-fluidpe-light-teal/30 pb-2">
                        <span className="text-gray-700">Loan Amount</span>
                        <span className="font-medium text-fluidpe-teal">
                          ₹{loanAmount.toLocaleString()}
                        </span>
                      </div>

                      <div className="flex items-center justify-between border-b border-fluidpe-light-teal/30 pb-2">
                        <span className="text-gray-700">Interest Rate</span>
                        <div className="flex items-center">
                          <span className="font-medium text-fluidpe-teal">
                            {interestRate}% p.a.
                          </span>
                          <div className="ml-2 rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-700">
                            Lowest!
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-gray-700">Tenure</span>
                        <span className="font-medium text-fluidpe-teal">
                          {loanDuration} months
                        </span>
                      </div>

                      <div className="mt-4 border-t border-fluidpe-light-teal/30 pt-2 text-center text-sm">
                        {/* <p className="font-medium text-fluidpe-medium-teal">
                          No hidden charges • No prepayment penalty • Quick
                          approval
                        </p> */}
                        <div className="mt-3 flex items-center justify-center rounded-lg border border-green-100 bg-green-50 p-2">
                          <span className="flex items-center text-sm font-medium text-green-700">
                            {/* <Star className="mr-1 h-4 w-4 text-yellow-500" />{" "} */}
                            Total Interest: ₹
                            {savings.ourInterest.toLocaleString()}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </AnimatedElement>

            <AnimatedElement delay={300} animation="fade-up" className="h-full">
              <div className="premium-card h-full overflow-hidden">
                <div className="bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal px-6 py-4">
                  <h3 className="flex items-center text-xl font-semibold text-white">
                    <CreditCard className="mr-2 h-5 w-5" /> Savings Comparison
                  </h3>
                  <p className="text-sm text-white/80">
                    See how much you save with Fluidpe compared to other options
                  </p>
                </div>

                <div className="p-6">
                  <div className="mb-6 overflow-hidden rounded-xl border border-fluidpe-light-teal/30 shadow-md">
                    <Table>
                      <TableHeader className="bg-gradient-to-r from-fluidpe-light-teal/70 to-fluidpe-light-gold/50">
                        <TableRow>
                          <TableHead className="font-semibold text-fluidpe-teal">
                            Loan Type
                          </TableHead>
                          <TableHead className="font-semibold text-fluidpe-teal">
                            Interest Rate
                          </TableHead>
                          <TableHead className="font-semibold text-fluidpe-teal">
                            Total Interest
                          </TableHead>
                          <TableHead className="font-semibold text-fluidpe-teal">
                            Comparison
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        <TableRow className="bg-gradient-to-r from-fluidpe-light-teal/30 to-transparent">
                          <TableCell className="font-medium">
                            <div className="flex items-center">
                              <Star className="mr-1.5 h-4 w-4 text-yellow-500" />
                              <span>Fluidpe Loan Against Mutual Funds</span>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium text-fluidpe-teal">
                            {interestRate}%
                          </TableCell>
                          <TableCell>
                            ₹{savings.ourInterest.toLocaleString()}
                          </TableCell>
                          <TableCell className="font-semibold text-green-600">
                            <div className="flex items-center">
                              <CheckCircle className="mr-1.5 h-4 w-4 text-green-500" />
                              <span>Best Option</span>
                            </div>
                          </TableCell>
                        </TableRow>
                        <TableRow className="transition-colors hover:bg-fluidpe-light-teal/10">
                          <TableCell className="font-medium">
                            Personal Loan
                          </TableCell>
                          <TableCell>14%</TableCell>
                          <TableCell>
                            ₹{savings.personalLoanExtra.toLocaleString()}
                          </TableCell>
                          <TableCell className="font-semibold text-red-600">
                            <AnimatedElement
                              animation="fade-in"
                              className="flex items-center"
                            >
                              <TrendingDown className="mr-1.5 h-4 w-4 text-red-500" />
                              <span>
                                -₹{savings.personalLoan.toLocaleString()}
                              </span>
                            </AnimatedElement>
                          </TableCell>
                        </TableRow>
                        <TableRow className="transition-colors hover:bg-fluidpe-light-teal/10">
                          <TableCell className="font-medium">
                            Credit Card
                          </TableCell>
                          <TableCell>36%</TableCell>
                          <TableCell>
                            ₹{savings.creditCardExtra.toLocaleString()}
                          </TableCell>
                          <TableCell className="font-semibold text-red-600">
                            <AnimatedElement
                              animation="fade-in"
                              className="flex items-center"
                            >
                              <TrendingDown className="mr-1.5 h-4 w-4 text-red-500" />
                              <span>
                                -₹{savings.creditCard.toLocaleString()}
                              </span>
                            </AnimatedElement>
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </div>

                  <div className="mb-4 mt-6">
                    <h4 className="mb-4 flex items-center text-lg font-semibold text-fluidpe-teal">
                      <Award className="mr-2 h-5 w-5" /> Highest Savings
                      Potential
                    </h4>
                    <div className="savings-highlight rounded-xl border border-green-100 bg-gradient-to-r from-green-50 to-fluidpe-light-teal/30 p-4 shadow-md">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium text-gray-700">
                            By choosing Fluidpe instead of Credit Card
                            financing:
                          </p>
                          <p className="mt-2 flex items-center text-xl font-bold text-green-600">
                            <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
                            You save ₹{savings.creditCard.toLocaleString()}
                          </p>
                          <p className="mt-1 text-xs text-green-600">
                            That's{" "}
                            {Math.round(
                              (savings.creditCard /
                                (loanAmount *
                                  (36 / 100) *
                                  (loanDuration / 12))) *
                                100,
                            )}
                            % in interest savings!
                          </p>
                        </div>
                        <div className="rounded-full bg-white p-3 shadow-md">
                          <TrendingUp className="h-8 w-8 text-green-500" />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 grid grid-cols-1 gap-4">
                    <AnimatedElement
                      delay={200}
                      className="rounded-xl border border-red-100 bg-red-50 p-4 shadow-md"
                    >
                      <div className="flex items-center">
                        <div className="mr-4 rounded-full bg-red-100 p-2">
                          <AlertTriangle className="h-5 w-5 text-red-500" />
                        </div>
                        <div>
                          <h4 className="text-md font-semibold text-red-700">
                            Avoid Credit Card Financing
                          </h4>
                          <p className="text-xs text-red-600">
                            You'll pay{" "}
                            <span className="font-bold">
                              ₹{savings.creditCard.toLocaleString()}
                            </span>{" "}
                            more in interest with credit cards at 36% rate
                          </p>
                        </div>
                      </div>
                    </AnimatedElement>

                    <AnimatedElement
                      delay={300}
                      className="rounded-xl border border-red-100/50 bg-red-50/50 p-4 shadow-md"
                    >
                      <div className="flex items-center">
                        <div className="mr-4 rounded-full bg-red-100 p-2">
                          <AlertTriangle className="h-5 w-5 text-red-500" />
                        </div>
                        <div>
                          <h4 className="text-md font-semibold text-red-700">
                            Personal Loans Cost More
                          </h4>
                          <p className="text-xs text-red-600">
                            You'll pay{" "}
                            <span className="font-bold">
                              ₹{savings.personalLoan.toLocaleString()}
                            </span>{" "}
                            more in interest with personal loans at 14% rate
                          </p>
                        </div>
                      </div>
                    </AnimatedElement>
                  </div>
                </div>
              </div>
            </AnimatedElement>
          </div>
        </div>
      </div>
    </section>
  );
}
