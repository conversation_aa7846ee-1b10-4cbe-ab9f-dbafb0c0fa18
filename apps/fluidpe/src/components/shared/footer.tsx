import React from "react";
import Image from "next/image";
import Link from "next/link";
import { FOOTER } from "@/lib/constants";

const Footer: React.FC = () => {
  return (
    <footer className="bg-fluidpe-teal text-white">
      <div className="container mx-auto px-4 py-12 md:py-16">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4">
          <div>
            <Image
              src="/static/logos/logo-white-without-bg.svg"
              alt="Fluidpe"
              height={100}
              width={100}
              className="mb-4 size-fit h-10"
            />
            <p className="mb-4 text-gray-300">
              Fluidpe offers innovative loan against mutual funds solutions with
              competitive interest rates starting at 8.5% and flexible repayment
              options.
            </p>
            {/* <div className="flex space-x-4">
              {FOOTER.socialLinks.map((link, index) => (
                <Link
                  key={index}
                  href={link.href}
                  className="text-gray-300 transition-colors hover:text-white"
                >
                  {link.icon}
                </Link>
              ))}
            </div> */}
          </div>

          <div>
            <h3 className="mb-4 text-lg font-semibold">Quick Links</h3>
            <ul className="space-y-2">
              {FOOTER.quickLinks.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-300 transition-colors hover:text-white"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="mb-4 text-lg font-semibold">Resources</h3>
            <ul className="space-y-2">
              {FOOTER.resourceLinks.map((link, index) => (
                <li key={index}>
                  <Link
                    href={link.href}
                    className="text-gray-300 transition-colors hover:text-white"
                  >
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          <div>
            <h3 className="mb-4 text-lg font-semibold">Contact Us</h3>
            <ul className="space-y-4">
              {FOOTER.contactInfo.map((item, index) => (
                <li key={index} className="flex items-start">
                  <span>{item.icon}</span>
                  {item.href ? (
                    <Link
                      href={item.href}
                      className="text-gray-300 transition-colors hover:text-white"
                    >
                      {item.content}
                    </Link>
                  ) : (
                    <span className="text-gray-300">{item.content}</span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        </div>

        <div className="mt-10 border-t border-gray-700 pt-6 text-center text-gray-300">
          <p>
            &copy; {new Date().getFullYear()} Fluidpe. All rights reserved.
            Neocollat Fintech Private Ltd
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
