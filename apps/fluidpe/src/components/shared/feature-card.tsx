import type { LucideIcon } from "lucide-react";
import React from "react";

import AnimatedElement from "./animated-element";

interface FeatureCardProps {
  icon: LucideIcon;
  title: string;
  description: string;
  delay: number;
  animation?: "fade-up" | "fade-left" | "fade-right" | "fade-down" | "zoom-in";
  color?: string;
}

const FeatureCard: React.FC<FeatureCardProps> = ({
  icon: Icon,
  title,
  description,
  delay,
  animation = "fade-up",
  color,
}) => {
  return (
    <AnimatedElement delay={delay} animation={animation} className="w-full">
      <div className="feature-card group relative h-full overflow-hidden rounded-xl border border-gray-100/30 bg-gradient-to-br from-white to-fluidpe-light-teal/10 p-6 shadow-sm transition-all duration-300 hover:-translate-y-2 hover:transform hover:shadow-md">
        <div className="duration-1500 absolute left-0 top-0 h-full w-full -translate-x-full bg-gradient-to-r from-transparent via-white/40 to-transparent opacity-0 transition-all ease-in-out group-hover:translate-x-full group-hover:opacity-100"></div>
        <div
          className={`${color || "bg-fluidpe-light-teal"} relative z-10 mb-4 flex h-14 w-14 items-center justify-center rounded-full p-3 transition-all duration-300 group-hover:bg-fluidpe-teal group-hover:text-white`}
        >
          <Icon className="h-8 w-8 text-fluidpe-teal transition-colors duration-300 group-hover:text-white" />
        </div>
        <h3 className="relative z-10 mb-2 text-xl font-semibold text-fluidpe-teal transition-transform duration-300 group-hover:translate-x-1">
          {title}
        </h3>
        <div className="relative z-10 mb-3 h-0.5 w-0 bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal transition-all duration-500 group-hover:w-2/3"></div>
        <p className="relative z-10 text-gray-600 transition-colors duration-300 group-hover:text-gray-700">
          {description}
        </p>
      </div>
    </AnimatedElement>
  );
};

export default FeatureCard;
