import Image from "next/image";
import Link from "next/link";
import { Calendar } from "lucide-react";

import type { blog as blogType } from "@acme/db/schema";
import { Card, CardContent } from "@acme/ui/components/ui/card";

import AnimatedElement from "./animated-element";

const BlogCard = ({ blog }: { blog: typeof blogType.$inferSelect }) => {
  return (
    <AnimatedElement delay={10} animation="fade-up" className="w-full">
      <Card className="group h-full cursor-pointer overflow-hidden border border-gray-100 transition-all duration-300 hover:-translate-y-2 hover:shadow-lg">
        <Link href={`blogs/${blog.slug}`}>
          <div className="relative overflow-hidden">
            <div className="h-48 overflow-hidden">
              <Image
                src={blog.imageUrl ?? ""}
                alt={blog.title}
                height={1000}
                width={1000}
                className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
              />
            </div>
            <div className="absolute right-4 top-4 flex gap-2">
              {blog.tags?.map((tag) => (
                <span
                  key={tag}
                  className="rounded-full bg-fluidpe-teal px-2 py-1 text-xs text-white"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
          <CardContent className="p-5">
            <div className="mb-3 flex items-center text-sm text-gray-500">
              <Calendar className="mr-1 h-4 w-4" />
              <span>{new Date(blog.createdAt).toDateString()}</span>
              <span className="mx-2">•</span>
              <span>{blog.authorName}</span>
            </div>
            <h3 className="mb-3 line-clamp-2 text-lg font-semibold transition-colors group-hover:text-fluidpe-teal">
              {blog.title}
            </h3>
            <p className="mb-4 line-clamp-3 text-sm text-gray-600">
              {blog.excerpt}
            </p>
            <p className="text-sm font-medium text-fluidpe-teal hover:underline">
              Read More
            </p>
          </CardContent>
        </Link>
      </Card>
    </AnimatedElement>
  );
};

export default BlogCard;
