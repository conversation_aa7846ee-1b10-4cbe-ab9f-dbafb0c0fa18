"use client";

import React, { useState } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";

import AnimatedElement from "./animated-element";

interface FaqItemProps {
  question: string;
  answer: string;
  delay: number;
}

const FaqItem: React.FC<FaqItemProps> = ({ question, answer, delay }) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <AnimatedElement delay={delay} animation="fade-left" className="w-full">
      <div className="border-b border-gray-200 pb-4 transition-colors duration-300 hover:border-fluidpe-teal">
        <button
          className="group flex w-full items-center justify-between py-4 text-left font-medium text-fluidpe-teal focus:outline-none"
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className="transition-transform duration-300 group-hover:translate-x-1">
            {question}
          </span>
          {isOpen ? (
            <ChevronUp className="h-5 w-5 text-fluidpe-teal transition-transform duration-300 group-hover:scale-110" />
          ) : (
            <ChevronDown className="h-5 w-5 text-fluidpe-teal transition-transform duration-300 group-hover:scale-110" />
          )}
        </button>
        <div
          className={`overflow-hidden transition-all duration-500 ${
            isOpen ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
          }`}
        >
          <p className="py-4 text-gray-600">{answer}</p>
        </div>
      </div>
    </AnimatedElement>
  );
};

export default FaqItem;
