import { Quote } from "lucide-react";

import { testimonial as testimonialType } from "@acme/db/schema";
import {
  Avatar,
  AvatarFallback,
  AvatarImage,
} from "@acme/ui/components/ui/avatar";

import AnimatedElement from "./animated-element";

const TestimonialCard = ({
  testimonial,
}: {
  testimonial: typeof testimonialType.$inferSelect;
}) => {
  // Generate a unique but consistent avatar URL based on the author's name
  const getAvatarUrl = (name: string) => {
    const seed = name.replace(/\s+/g, "").toLowerCase();
    // Changed to use 'adventurer' style which has happier-looking faces
    return `https://api.dicebear.com/7.x/adventurer/svg?seed=${seed}&mouth=smile,laugh&eyes=happy,wink&backgroundColor=b6e3f4,c0aede,d1d4f9`;
  };

  // Get initials for avatar fallback
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase();
  };

  return (
    <AnimatedElement delay={10} animation="zoom-in" className="w-full">
      <div className="group relative flex h-full flex-col overflow-hidden rounded-xl border border-gray-100 bg-gradient-to-br from-white to-fluidpe-light-teal/20 p-6 shadow-lg transition-all duration-300 hover:-translate-y-2 hover:transform hover:shadow-2xl">
        <div className="duration-1500 absolute left-0 top-0 h-full w-full -translate-x-full bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 transition-all ease-in-out group-hover:translate-x-full group-hover:opacity-100"></div>
        <Quote className="mb-4 h-8 w-8 text-fluidpe-teal opacity-40 transition-transform duration-300 group-hover:scale-110" />
        <p className="relative mb-6 flex-grow text-gray-700">
          <span className="relative z-10 transition-all duration-300 group-hover:text-fluidpe-teal/80">
            {testimonial.content}
          </span>
        </p>
        <div className="relative z-10 flex items-center">
          <Avatar className="mr-3 h-12 w-12 border-2 border-fluidpe-light-teal shadow-sm transition-all duration-300 group-hover:border-fluidpe-teal">
            <AvatarImage
              src={testimonial.imageUrl || getAvatarUrl(testimonial.name)}
              alt={testimonial.name}
            />
            <AvatarFallback className="bg-gradient-to-br from-fluidpe-light-teal to-fluidpe-medium-teal text-white">
              {getInitials(testimonial.name)}
            </AvatarFallback>
          </Avatar>
          <div>
            <p className="font-semibold text-fluidpe-teal transition-transform duration-300 group-hover:translate-x-1">
              {testimonial.name}
            </p>
            <p className="text-sm text-gray-500 transition-transform duration-500 group-hover:translate-x-1">
              {testimonial.designation}, {testimonial.location}
            </p>
            <div className="mt-2 h-0.5 w-0 bg-gradient-to-r from-fluidpe-teal to-fluidpe-medium-teal transition-all duration-500 group-hover:w-full"></div>
          </div>
        </div>
      </div>
    </AnimatedElement>
  );
};

export default TestimonialCard;
