import React from "react";

import AnimatedElement from "./animated-element";

interface ProcessStepProps {
  number: number;
  title: string;
  description: string;
  delay: number;
  icon?: React.ReactNode;
}

const ProcessStep: React.FC<ProcessStepProps> = ({
  number,
  title,
  description,
  delay,
  icon,
}) => {
  return (
    <AnimatedElement delay={delay} animation="fade-up" className="w-full">
      <div className="group relative flex h-full flex-col items-center rounded-xl border border-transparent bg-white px-6 py-8 text-center transition-all duration-500 hover:-translate-y-2 hover:border-fluidpe-light-teal/50 hover:shadow-lg">
        <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full border border-fluidpe-teal font-bold shadow-md transition-transform duration-300 group-hover:scale-110">
          {number}
        </div>
        {icon && (
          <div className="mb-4 transform text-fluidpe-teal transition-transform duration-300 group-hover:scale-110">
            {icon}
          </div>
        )}

        <div>
          <h3 className="mb-3 text-xl font-semibold text-fluidpe-teal">
            {title}
          </h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>
    </AnimatedElement>
  );
};

export default ProcessStep;
