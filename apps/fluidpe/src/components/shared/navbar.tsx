"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { env } from "@/env";
import { Menu, X } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);

    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  return (
    <header
      className={`fixed left-0 right-0 top-0 z-50 transition-all duration-300 ${
        isScrolled
          ? "bg-white py-3 shadow-md"
          : isMenuOpen
            ? "bg-white"
            : "bg-transparent py-5"
      }`}
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex items-center justify-between">
          <Link href="/" className="flex items-center">
            <img
              src="/static/logos/logo-night-without-bg.png"
              alt="Fluidpe"
              className="h-10 md:h-12"
            />
          </Link>

          <nav className="hidden items-center space-x-8 lg:flex">
            <NavLinks />
          </nav>
          <div className="flex flex-row gap-3">
            <Link
              href={env.NEXT_PUBLIC_CUSTOMER_WEBAPP_URL}
              target="_blank"
              className="hidden items-center space-x-4 lg:flex"
            >
              <Button className="bg-fluidpe-teal text-white hover:bg-fluidpe-medium-teal">
                Partner Login{" "}
              </Button>
            </Link>
            <Link
              href={env.NEXT_PUBLIC_CUSTOMER_WEBAPP_URL}
              target="_blank"
              className="hidden items-center space-x-4 lg:flex"
            >
              <Button className="bg-fluidpe-teal text-white hover:bg-fluidpe-medium-teal">
                Apply Now
              </Button>
            </Link>
          </div>
          <button
            className="lg:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            {isMenuOpen ? (
              <X className="h-6 w-6 text-fluidpe-teal" />
            ) : (
              <Menu className="h-6 w-6 text-fluidpe-teal" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile menu */}
      {isMenuOpen && (
        <div className="lg:hidden">
          <div className="bg-white p-4 shadow-lg">
            <nav className="mb-4 flex flex-col space-y-4">
              <NavLinks mobile onClick={() => setIsMenuOpen(false)} />
            </nav>
            <div className="flex flex-col space-y-2">
              <Button className="w-full bg-fluidpe-teal text-white hover:bg-fluidpe-medium-teal">
                Partner Login{" "}
              </Button>{" "}
              <Button className="w-full bg-fluidpe-teal text-white hover:bg-fluidpe-medium-teal">
                Apply Now
              </Button>
            </div>
          </div>
        </div>
      )}
    </header>
  );
};

const NavLinks = ({ mobile = false, onClick = () => {} }) => {
  const linkClasses = mobile
    ? "text-fluidpe-teal hover:text-fluidpe-medium-teal font-medium text-lg py-2"
    : "text-fluidpe-teal hover:text-fluidpe-medium-teal font-medium transition-colors duration-200";

  return (
    <>
      <Link href="/#features" className={linkClasses}>
        Why Choose FluidPe
      </Link>
      <Link href="/#how-it-works" className={linkClasses}>
        How It Works
      </Link>
      <Link href="/#benefits" className={linkClasses}>
        Interest Calculator
      </Link>
      <Link href="/blogs" className={linkClasses}>
        Blogs
      </Link>
      <Link href="/#faq" className={linkClasses}>
        FAQ
      </Link>
    </>
  );
};

export default Navbar;
