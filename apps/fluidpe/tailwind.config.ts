import type { Config } from "tailwindcss";
import { fontFamily } from "tailwindcss/defaultTheme";

import webConfig from "@acme/tailwind-config/web";

export default {
  // Extend from web config which already incorporates the base config
  presets: [webConfig],
  // Override content paths for this specific app
  content: [
    "./src/**/*.{js,ts,jsx,tsx}",
    "./pages/**/*.{js,ts,jsx,tsx}",
    "./app/**/*.{js,ts,jsx,tsx}",
    "./components/**/*.{js,ts,jsx,tsx}",
    "../../packages/ui/src/**/*.{ts,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ["var(--font-geist-sans)", ...fontFamily.sans],
        mono: ["var(--font-geist-mono)", ...fontFamily.mono],
      },
    },
  },
} satisfies Config;
