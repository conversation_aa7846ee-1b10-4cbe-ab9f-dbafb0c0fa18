import { fileURLToPath } from "url";
import create<PERSON><PERSON> from "jiti";

// Import env files to validate at build time. Use jiti so we can load .ts files in here.
createJiti(fileURLToPath(import.meta.url))("./src/env");

/** @type {import("next").NextConfig} */
const config = {
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        hostname: "*.ufs.sh",
        protocol: "https",
      },
      {
        hostname: "picsum.photos",
        protocol: "https",
      },
    ],
  },
  //   webpack: (config, { isServer }) => {
  //     if (isServer) {
  //       config.plugins = [...config.plugins, new PrismaPlugin()];
  //     }

  //     return config;
  //   },
  /** Enables hot reloading for local packages without a build step */
  transpilePackages: [
    "@acme/api",
    "@acme/auth",
    "@acme/db",
    "@acme/ui",
    "@acme/validators",
  ],
  experimental: {
    // nodeMiddleware: true,
  },
  /** We already do linting and typechecking as separate tasks in CI */
  eslint: { ignoreDuringBuilds: true },
  //   typescript: { ignoreBuildErrors: true },
};

export default config;
