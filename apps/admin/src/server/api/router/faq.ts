import { z } from "zod";

import { asc, desc, eq, inArray } from "@acme/db";
import { faq } from "@acme/db/schema";
import { FaqCRUDSchema } from "@acme/validators/admin";

import { createTRPCRouter, protectedProcedure } from "../trpc";

export const faqRouter = createTRPCRouter({
  getAllFaqs: protectedProcedure.query(async ({ ctx }) => {
    try {
      return await ctx.db.query.faq.findMany({
        orderBy: asc(faq.order),
      });
    } catch (error) {
      throw new Error(
        `Failed to fetch FAQs: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }),

  getFaqById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const faqData = await ctx.db.query.faq.findFirst({
          where: eq(faq.id, input.id),
        });
        if (!faqData) {
          throw new Error(`FAQ with ID ${input.id} not found`);
        }
        return faqData;
      } catch (error) {
        throw new Error(
          `Failed to fetch FAQ: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),

  createFaq: protectedProcedure
    .input(FaqCRUDSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // Find the current highest order value
        const highestOrderFaq = await ctx.db.query.faq.findFirst({
          orderBy: desc(faq.order),
        });

        // Set the new FAQ's order to be one higher than the current highest
        const newOrder = highestOrderFaq ? highestOrderFaq.order + 1 : 1;

        return await ctx.db.insert(faq).values({
          ...input,
          order: newOrder,
        });
      } catch (error) {
        throw new Error(
          `Failed to create FAQ: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),

  updateFaq: protectedProcedure
    .input(FaqCRUDSchema.extend({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, ...data } = input;
        if (!id) throw new Error("FAQ ID is required for update");

        const existingFaq = await ctx.db.query.faq.findFirst({
          where: eq(faq.id, id),
        });

        if (!existingFaq) {
          throw new Error(`FAQ with ID ${id} not found`);
        }

        return await ctx.db.update(faq).set(data).where(eq(faq.id, id));
      } catch (error) {
        throw new Error(
          `Failed to update FAQ: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),

  deleteFaq: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const existingFaq = await ctx.db.query.faq.findFirst({
          where: eq(faq.id, input.id),
        });

        if (!existingFaq) {
          throw new Error(`FAQ with ID ${input.id} not found`);
        }

        // Delete the FAQ
        await ctx.db.delete(faq).where(eq(faq.id, input.id));

        // Normalize all remaining FAQ orders to ensure they are consecutive
        const remainingFaqs = await ctx.db.query.faq.findMany({
          orderBy: asc(faq.order),
        });

        await ctx.db.transaction(async (tx) => {
          const updatePromises = remainingFaqs.map((faqItem, index) => {
            return tx
              .update(faq)
              .set({ order: index + 1 })
              .where(eq(faq.id, faqItem.id));
          });

          await Promise.all(updatePromises);
        });

        return { success: true };
      } catch (error) {
        throw new Error(
          `Failed to delete FAQ: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),

  deleteFaqs: protectedProcedure
    .input(z.object({ ids: z.array(z.string()) }))
    .mutation(async ({ ctx, input }) => {
      try {
        if (input.ids.length === 0) {
          throw new Error("No FAQ IDs provided for deletion");
        }

        // Delete all selected FAQs
        await ctx.db.delete(faq).where(inArray(faq.id, input.ids));

        // Normalize all remaining FAQ orders to ensure they are consecutive
        const remainingFaqs = await ctx.db.query.faq.findMany({
          orderBy: asc(faq.order),
        });

        // Update all FAQ orders to be consecutive integers starting from 1

        await ctx.db.transaction(async (tx) => {
          const updatePromises = remainingFaqs.map((faqItem, index) => {
            return tx
              .update(faq)
              .set({ order: index + 1 })
              .where(eq(faq.id, faqItem.id));
          });

          await Promise.all(updatePromises);
        });

        return { success: true, count: input.ids.length };
      } catch (error) {
        throw new Error(
          `Failed to delete FAQs: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),

  updateFaqOrder: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        direction: z.enum(["up", "down"]),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        // First, normalize all FAQ orders to ensure they are consecutive integers
        const allFaqs = await ctx.db.query.faq.findMany({
          orderBy: asc(faq.order),
        });

        // Normalize all FAQ orders first to ensure consistency

        await ctx.db.transaction(async (tx) => {
          const normalizePromises = allFaqs.map((faqItem, index) => {
            return tx
              .update(faq)
              .set({ order: index + 1 })
              .where(eq(faq.id, faqItem.id));
          });

          await Promise.all(normalizePromises);
        });

        // Fetch the updated FAQs after normalization
        const updatedFaqs = await ctx.db.query.faq.findMany({
          orderBy: asc(faq.order),
        });

        // Find the current FAQ after normalization
        const currentIndex = updatedFaqs.findIndex(
          (faq) => faq.id === input.id,
        );
        if (currentIndex === -1) {
          throw new Error(`FAQ with ID ${input.id} not found`);
        }

        // Check if move is possible
        if (
          (input.direction === "up" && currentIndex === 0) ||
          (input.direction === "down" &&
            currentIndex === updatedFaqs.length - 1)
        ) {
          throw new Error(`Cannot move FAQ ${input.direction}`);
        }

        // Get the adjacent FAQ
        const targetIndex =
          input.direction === "up" ? currentIndex - 1 : currentIndex + 1;

        // Get the two FAQs that need to be swapped
        const currentFaq = updatedFaqs[currentIndex];
        const targetFaq = updatedFaqs[targetIndex];

        if (!targetFaq || !currentFaq) {
          throw new Error("Failed to find adjacent FAQs for reordering");
        }

        // Use a transaction to swap the orders
        await ctx.db.transaction(async (tx) => {
          await tx
            .update(faq)
            .set({ order: targetIndex + 1 })
            .where(eq(faq.id, currentFaq.id));
          await tx
            .update(faq)
            .set({ order: currentIndex + 1 })
            .where(eq(faq.id, targetFaq.id));
        });

        return { success: true };
      } catch (error) {
        throw new Error(
          `Failed to reorder FAQ: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),
});

export default faqRouter;
