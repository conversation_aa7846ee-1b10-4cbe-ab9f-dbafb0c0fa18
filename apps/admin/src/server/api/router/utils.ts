import { z } from "zod";

import { utapi } from "~/lib/utils";
import { createTRPCRouter, protectedProcedure } from "../trpc";

// This router is used to handle utility functions that are not specific to any particular feature
const utilsRouter = createTRPCRouter({
  deleteUploadthingFileUsingFileKey: protectedProcedure
    .input(z.object({ fileKey: z.string() }))
    .mutation(async ({ input }) => {
      try {
        await utapi.deleteFiles(input.fileKey);
        return { success: true };
      } catch (error) {
        console.error("Error deleting file:", error);
        throw new Error("Failed to delete file");
      }
    }),
});

export default utilsRouter;
