import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { desc, eq } from "@acme/db";
import { blog } from "@acme/db/schema";
import { BlogCRUDSchema } from "@acme/validators/admin";

import { slugify } from "~/lib/utils";
import { createTRPCRouter, protectedProcedure } from "../trpc";

export const blogRouter = createTRPCRouter({
  getAllBlogs: protectedProcedure.query(async ({ ctx }) => {
    try {
      return await ctx.db.query.blog.findMany({
        orderBy: desc(blog.createdAt),
      });
    } catch (err) {
      if (err instanceof TRPCError) {
        throw err;
      }

      throw new TRPCError({
        code: "BAD_REQUEST",
        message: "Failed to fetch blogs",
      });
    }
  }),

  getBlogById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        return await ctx.db.query.blog.findFirst({
          where: eq(blog.id, input.id),
        });
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to fetch blog",
        });
      }
    }),

  createBlog: protectedProcedure
    .input(BlogCRUDSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const slug = slugify(input.title);

        // Check if blog with this slug already exists
        const existingBlog = await ctx.db.query.blog.findFirst({
          where: eq(blog.slug, slug),
        });

        if (existingBlog) {
          throw new TRPCError({
            code: "CONFLICT",
            message: "A blog with this title already exists",
          });
        }

        return await ctx.db.insert(blog).values({
          title: input.title,
          slug,
          excerpt: input.excerpt,
          content: input.content,
          imageUrl: input.imageUrl,
          authorName: input.authorName,
          tags: input.tags,
          published: input.published,
        });
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid blog data",
        });
      }
    }),

  updateBlog: protectedProcedure
    .input(
      BlogCRUDSchema.extend({
        id: z.string(),
      }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, ...data } = input;
        if (!id)
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Blog ID is required for update",
          });

        await ctx.db.update(blog).set(data).where(eq(blog.id, id));

        return {
          message: "Blog updated successfully",
        };
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Invalid blog data",
        });
      }
    }),

  deleteBlog: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        return await ctx.db.delete(blog).where(eq(blog.id, input.id));
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to delete blog",
        });
      }
    }),

  togglePublishStatus: protectedProcedure
    .input(z.object({ id: z.string(), published: z.boolean() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, published } = input;
        return await ctx.db
          .update(blog)
          .set({ published })
          .where(eq(blog.id, id));
      } catch (err) {
        if (err instanceof TRPCError) {
          throw err;
        }

        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Failed to update publish status",
        });
      }
    }),
});

export default blogRouter;
