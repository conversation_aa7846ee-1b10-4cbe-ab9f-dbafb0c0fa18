import { z } from "zod";

import { desc, eq } from "@acme/db";
import { testimonial } from "@acme/db/schema";
import { TestimonialCRUDSchema } from "@acme/validators/admin";

import { createTRPCRouter, protectedProcedure } from "../trpc";

export const testimonialRouter = createTRPCRouter({
  getAllTestimonials: protectedProcedure.query(async ({ ctx }) => {
    try {
      return await ctx.db.query.testimonial.findMany({
        orderBy: desc(testimonial.createdAt),
      });
    } catch (error) {
      throw new Error(
        `Failed to fetch testimonials: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }),

  getTestimonialById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const testimonialRes = await ctx.db.query.testimonial.findFirst({
          where: eq(testimonial.id, input.id),
        });
        if (!testimonialRes) {
          throw new Error(`Testimonial with ID ${input.id} not found`);
        }
        return testimonialRes;
      } catch (error) {
        throw new Error(
          `Failed to fetch testimonial: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),

  createTestimonial: protectedProcedure
    .input(TestimonialCRUDSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        return await ctx.db.insert(testimonial).values(input);
      } catch (error) {
        throw new Error(
          `Failed to create testimonial: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),

  updateTestimonial: protectedProcedure
    .input(TestimonialCRUDSchema.extend({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const { id, ...data } = input;
        if (!id) throw new Error("Testimonial ID is required for update");

        const existingTestimonial = await ctx.db.query.testimonial.findFirst({
          where: eq(testimonial.id, id),
        });

        if (!existingTestimonial) {
          throw new Error(`Testimonial with ID ${id} not found`);
        }

        return await ctx.db
          .update(testimonial)
          .set(data)
          .where(eq(testimonial.id, id));
      } catch (error) {
        throw new Error(
          `Failed to update testimonial: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),

  deleteTestimonial: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const existingTestimonial = await ctx.db.query.testimonial.findFirst({
          where: eq(testimonial.id, input.id),
        });

        if (!existingTestimonial) {
          throw new Error(`Testimonial with ID ${input.id} not found`);
        }

        return await ctx.db
          .delete(testimonial)
          .where(eq(testimonial.id, input.id));
      } catch (error) {
        throw new Error(
          `Failed to delete testimonial: ${error instanceof Error ? error.message : String(error)}`,
        );
      }
    }),
});

export default testimonialRouter;
