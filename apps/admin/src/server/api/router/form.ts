import { TRPCError } from "@trpc/server";
import { z } from "zod";

import { desc, eq } from "@acme/db";
import { contactRequest, contactStatus } from "@acme/db/schema";

import { createTRPCRouter, protectedProcedure } from "../trpc";

const contactRequestRouter = createTRPCRouter({
  getAllContactRequests: protectedProcedure.query(async ({ ctx }) => {
    try {
      return await ctx.db.query.contactRequest.findMany({
        orderBy: desc(contactRequest.createdAt),
      });
    } catch (error) {
      if (error instanceof TRPCError) throw error;

      throw new TRPCError({
        code: "INTERNAL_SERVER_ERROR",
        message: `Failed to fetch contact requests: ${error instanceof Error ? error.message : String(error)}`,
        cause: error,
      });
    }
  }),

  getContactRequestById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      try {
        const contactRequestRes = await ctx.db.query.contactRequest.findFirst({
          where: eq(contactRequest.id, input.id),
        });

        if (!contactRequestRes) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Contact request with ID ${input.id} not found`,
          });
        }

        return contactRequestRes;
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to fetch contact request: ${error instanceof Error ? error.message : String(error)}`,
          cause: error,
        });
      }
    }),

  updateContactRequestStatus: protectedProcedure
    .input(
      z.object({ id: z.string(), status: z.enum(contactStatus.enumValues) }),
    )
    .mutation(async ({ ctx, input }) => {
      try {
        const existingRequest = await ctx.db.query.contactRequest.findFirst({
          where: eq(contactRequest.id, input.id),
        });

        if (!existingRequest) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Contact request with ID ${input.id} not found`,
          });
        }

        return await ctx.db
          .update(contactRequest)
          .set({ status: input.status })
          .where(eq(contactRequest.id, input.id));
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to update contact request status: ${error instanceof Error ? error.message : String(error)}`,
          cause: error,
        });
      }
    }),

  deleteContactRequest: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      try {
        const existingRequest = await ctx.db.query.contactRequest.findFirst({
          where: eq(contactRequest.id, input.id),
        });

        if (!existingRequest) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: `Contact request with ID ${input.id} not found`,
          });
        }

        return await ctx.db
          .delete(contactRequest)
          .where(eq(contactRequest.id, input.id));
      } catch (error) {
        if (error instanceof TRPCError) throw error;

        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: `Failed to delete contact request: ${error instanceof Error ? error.message : String(error)}`,
          cause: error,
        });
      }
    }),
});

export default contactRequestRouter;
