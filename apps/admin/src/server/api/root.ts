import { authRouter } from "./router/auth";
import blogRouter from "./router/blog";
import faqRouter from "./router/faq";
import contactRequestRouter from "./router/form";
import testimonialRouter from "./router/testimonial";
import utilsRouter from "./router/utils";
import { createTRPCRouter } from "./trpc";

export const appRouter = createTRPCRouter({
  auth: authRouter,
  utils: utilsRouter,
  faq: faqRouter,
  blog: blogRouter,
  testimonial: testimonialRouter,
  contactRequest: contactRequestRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;
