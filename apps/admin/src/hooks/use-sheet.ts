"use client";

import { useCallback } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

/**
 * A custom hook to manage the state of a sheet (or modal) using URL query parameters.
 *
 * @param {string} queryParam - The query parameter key used to control the sheet's state (e.g., "bankId").
 * @returns {Object} - An object containing the sheet's state and control functions.
 * @property {boolean} isOpen - Indicates whether the sheet is open (true if the query parameter exists).
 * @property {string | null} paramValue - The value of the query parameter (null if the parameter doesn't exist).
 * @property {Function} openSheet - Opens the sheet and optionally sets a value for the query parameter.
 * @property {Function} closeSheet - Closes the sheet and removes the query parameter from the URL.
 */
export const useSheet = (queryParam: string) => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const isOpen = searchParams.has(queryParam);
  const paramValue = searchParams.get(queryParam);
  const sanitizedParamValue =
    paramValue && paramValue.length > 0 ? paramValue : null;

  // Helper function to create a query string with the given key and value. If the value is null or undefined, the key is removed from the query string.
  const createQueryString = useCallback(
    (name: string, value?: string | null) => {
      const params = new URLSearchParams(searchParams);
      if (value !== undefined && value !== null) {
        params.set(name, value);
      } else {
        params.delete(name);
      }
      return params.toString();
    },
    [searchParams],
  );

  // Opens the sheet and optionally sets a value for the query parameter. If no value is provided, the query parameter is set with an empty value.
  const openSheet = useCallback(
    (value?: string | null) => {
      router.replace(
        `${pathname}?${createQueryString(queryParam, value ?? "")}`,
        { scroll: false },
      );
    },
    [pathname, queryParam, createQueryString, router],
  );

  // Closes the sheet and removes the query parameter from the URL.
  const closeSheet = useCallback(() => {
    router.replace(`${pathname}?${createQueryString(queryParam, null)}`, {
      scroll: false,
    });
  }, [pathname, queryParam, createQueryString, router]);

  return {
    isOpen,
    paramValue: sanitizedParamValue,
    openSheet,
    closeSheet,
  };
};
