"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import { useState } from "react";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ChevronDown,
  ChevronUp,
  Edit,
  MoreHorizontal,
  Trash,
} from "lucide-react";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import { Input } from "@acme/ui/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";

import AlertWrapper from "~/components/shared/alert-wrapper";
import SortableColumnHeader from "~/components/shared/sortable-column-header";
import { useSheet } from "~/hooks/use-sheet";
import { faqParamName } from "~/lib/constants";
import { api } from "~/trpc/react";
import FaqFormSheet from "./faq-form-sheet";

export function FaqDataTable() {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const { openSheet } = useSheet(faqParamName);

  const { data: faqs = [], refetch } = api.faq.getAllFaqs.useQuery();
  const deleteFaq = api.faq.deleteFaq.useMutation({
    onSuccess: () => {
      toast.success("FAQ deleted successfully");
      void refetch();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const deleteFaqs = api.faq.deleteFaqs.useMutation({
    onSuccess: (data) => {
      toast.success(`${data.count} FAQs deleted successfully`);
      void refetch();
      setRowSelection({});
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const reorderFaq = api.faq.updateFaqOrder.useMutation({
    onSuccess: () => {
      toast.success("FAQ reordered successfully");
      void refetch();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const columns: ColumnDef<(typeof faqs)[number]>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "reorder",
      enableSorting: false,
      enableHiding: false,
      size: 60,
      header: () => <div className="text-center">Reorder</div>,
      cell: ({ row }) => {
        const faq = row.original;
        const currentIndex = faqs.findIndex((f) => f.id === faq.id);
        const isFirst = currentIndex === 0;
        const isLast = currentIndex === faqs.length - 1;

        return (
          <div className="flex items-center justify-center space-x-1">
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7"
              disabled={isFirst}
              onClick={() => reorderFaq.mutate({ id: faq.id, direction: "up" })}
            >
              <ChevronUp className="h-4 w-4" />
              <span className="sr-only">Move up</span>
            </Button>
            <Button
              variant="outline"
              size="icon"
              className="h-7 w-7"
              disabled={isLast}
              onClick={() =>
                reorderFaq.mutate({ id: faq.id, direction: "down" })
              }
            >
              <ChevronDown className="h-4 w-4" />
              <span className="sr-only">Move down</span>
            </Button>
          </div>
        );
      },
    },
    {
      accessorKey: "question",
      header: ({ column }) => {
        return <SortableColumnHeader column={column} title="Question" />;
      },
      cell: ({ row }) => (
        <div className="max-w-[500px] truncate font-medium">
          {row.getValue("question")}
        </div>
      ),
    },
    {
      accessorKey: "answer",
      header: ({ column }) => {
        return <SortableColumnHeader column={column} title="Answer" />;
      },
      cell: ({ row }) => (
        <div className="max-w-[500px] truncate">{row.getValue("answer")}</div>
      ),
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => {
        return <SortableColumnHeader column={column} title="Created At" />;
      },
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toDateString()}</div>;
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const faq = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => openSheet(faq.id)}>
                <Edit className="mr-2 h-4 w-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={(e) => e.stopPropagation()}
              >
                <AlertWrapper
                  trigger={
                    <button
                      className="flex items-center gap-2"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Trash className="size-4" /> Delete
                    </button>
                  }
                  onConfirm={() => deleteFaq.mutate({ id: faq.id })}
                  title="Are you sure you want to delete this FAQ?"
                  description="This action cannot be undone."
                />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data: faqs,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <Input
          placeholder="Filter questions..."
          value={table.getColumn("question")?.getFilterValue() as string}
          onChange={(event) =>
            table.getColumn("question")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <div className="flex gap-2">
          {table.getFilteredSelectedRowModel().rows.length > 0 && (
            <AlertWrapper
              trigger={
                <Button variant="destructive">
                  <Trash className="mr-2 h-4 w-4" />
                  Delete Selected (
                  {table.getFilteredSelectedRowModel().rows.length})
                </Button>
              }
              onConfirm={() => {
                const selectedIds = table
                  .getFilteredSelectedRowModel()
                  .rows.map((row) => row.original.id);
                deleteFaqs.mutate({ ids: selectedIds });
              }}
              title="Are you sure you want to delete these FAQs?"
              description="This action cannot be undone."
            />
          )}
          <FaqFormSheet />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
}
