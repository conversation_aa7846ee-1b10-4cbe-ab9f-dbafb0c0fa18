"use client";

import React from "react";
import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { faqParamName } from "~/lib/constants";
import { FaqCreateUpdateForm } from "./faq-create-update-form";

const FaqFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } = useSheet(faqParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Faq
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Faq" : "Create Faq"}
      >
        <FaqCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default FaqFormSheet;
