"use client";

import type { z } from "zod";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { skipToken } from "@tanstack/react-query";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { Textarea } from "@acme/ui/components/ui/textarea";
import { FaqCRUDSchema } from "@acme/validators/admin";

import { useSheet } from "~/hooks/use-sheet";
import { faqParamName } from "~/lib/constants";
import { api } from "~/trpc/react";

type FaqFormValues = z.infer<typeof FaqCRUDSchema>;

export function FaqCreateUpdateForm() {
  const { paramValue: faqId, closeSheet } = useSheet(faqParamName);

  const trpcUtils = api.useUtils();
  const router = useRouter();
  const isEditMode = !!faqId;

  const { data: faqData } = api.faq.getFaqById.useQuery(
    faqId ? { id: faqId } : skipToken,
  );

  const form = useForm<FaqFormValues>({
    resolver: zodResolver(FaqCRUDSchema),
    defaultValues: faqData
      ? {
          question: faqData.question,
          answer: faqData.answer,
        }
      : {
          question: "",
          answer: "",
        },
  });

  const createFaq = api.faq.createFaq.useMutation({
    onSuccess: () => {
      toast.success("FAQ created successfully");
      void trpcUtils.faq.getAllFaqs.invalidate();
      closeSheet();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const updateFaq = api.faq.updateFaq.useMutation({
    onSuccess: () => {
      toast.success("FAQ updated successfully");
      router.push("/faqs");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  function onSubmit(data: FaqFormValues) {
    if (isEditMode) {
      updateFaq.mutate({ ...data, id: faqId });
    } else {
      createFaq.mutate(data);
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
        <FormField
          control={form.control}
          name="question"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Question</FormLabel>
              <FormControl>
                <Input placeholder="Enter FAQ question" {...field} />
              </FormControl>
              <FormDescription>
                The question that will be displayed in the FAQ section.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="answer"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Answer</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter the answer to the question"
                  className="min-h-[150px]"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                The detailed answer to the question.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex gap-2">
          <Button type="submit">
            {isEditMode ? "Update FAQ" : "Create FAQ"}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/faqs")}
          >
            Cancel
          </Button>
        </div>
      </form>
    </Form>
  );
}
