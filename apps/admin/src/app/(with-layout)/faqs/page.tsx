import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { faqParamName } from "~/lib/constants";
import { api, HydrateClient } from "~/trpc/server";
import { FaqDataTable } from "./faq-data-table";

export const metadata = {
  title: "FAQs Management",
  description: "Manage your website's Frequently Asked Questions",
};

interface FaqsPageProps {
  searchParams: Promise<{ [faqParamName]?: string }>;
}

const FaqsPage = async ({ searchParams }: FaqsPageProps) => {
  const faqId = (await searchParams)[faqParamName];
  if (faqId) {
    await api.faq.getFaqById.prefetch({ id: faqId });
  }

  await api.faq.getAllFaqs.prefetch();

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              FAQs Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage FAQs that are displayed on your website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <FaqDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default FaqsPage;
