import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { testimonialParamName } from "~/lib/constants";
import { api, HydrateClient } from "~/trpc/server";
import { TestimonialDataTable } from "./testimonial-data-table";

export const metadata = {
  title: "Testimonials Management",
  description: "Manage your website's Testimonials",
};

interface TestimonialsPageProps {
  searchParams: Promise<{ [testimonialParamName]?: string }>;
}

const TestimonialsPage = async ({ searchParams }: TestimonialsPageProps) => {
  const testimonialId = (await searchParams)[testimonialParamName];
  if (testimonialId) {
    await api.testimonial.getTestimonialById.prefetch({ id: testimonialId });
  }

  await api.testimonial.getAllTestimonials.prefetch();

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Testimonials Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage testimonials that are displayed on your
              website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <TestimonialDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default TestimonialsPage;
