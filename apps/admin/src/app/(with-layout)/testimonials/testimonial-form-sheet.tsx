"use client";

import React from "react";
import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { testimonialParamName } from "~/lib/constants";
import { TestimonialCreateUpdateForm } from "./testimonial-create-update-form";

const TestimonialFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } =
    useSheet(testimonialParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Testimonial
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Testimonial" : "Create Testimonial"}
      >
        <TestimonialCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default TestimonialFormSheet;
