"use client";

import type { z } from "zod";
import Image from "next/image";
import { zodResolver } from "@hookform/resolvers/zod";
import { skipToken } from "@tanstack/react-query";
import {
  generateUploadButton,
  generateUploadDropzone,
} from "@uploadthing/react";
import { Loader2, Trash, Upload } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { Textarea } from "@acme/ui/components/ui/textarea";
import { TestimonialCRUDSchema } from "@acme/validators/admin";

import type { OurFileRouter } from "~/app/api/uploadthing/core";
import { useSheet } from "~/hooks/use-sheet";
import { testimonialParamName } from "~/lib/constants";
import { api } from "~/trpc/react";

export const UploadButton = generateUploadButton<OurFileRouter>();
export const UploadDropzone = generateUploadDropzone<OurFileRouter>();

type TestimonialFormValues = z.infer<typeof TestimonialCRUDSchema>;

export const TestimonialCreateUpdateForm = () => {
  const { paramValue: testimonialId, closeSheet } =
    useSheet(testimonialParamName);

  const trpcUtils = api.useUtils();
  const isEditMode = !!testimonialId;

  const { data: testimonialData } = api.testimonial.getTestimonialById.useQuery(
    testimonialId ? { id: testimonialId } : skipToken,
  );

  const form = useForm<TestimonialFormValues>({
    resolver: zodResolver(TestimonialCRUDSchema),
    defaultValues: testimonialData
      ? {
          name: testimonialData.name || "",
          designation: testimonialData.designation || "",
          location: testimonialData.location || "",
          imageUrl: testimonialData.imageUrl || "",
          content: testimonialData.content || "",
        }
      : {
          name: "",
          designation: "",
          location: "",
          imageUrl: "",
          content: "",
        },
  });

  const createTestimonial = api.testimonial.createTestimonial.useMutation({
    onSuccess: () => {
      toast.success("Testimonial created successfully");
      void trpcUtils.testimonial.getAllTestimonials.invalidate();
      closeSheet();
      form.reset();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const updateTestimonial = api.testimonial.updateTestimonial.useMutation({
    onSuccess: () => {
      toast.success("Testimonial updated successfully");
      void trpcUtils.testimonial.getAllTestimonials.invalidate();
      closeSheet();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const deleteProfileImage =
    api.utils.deleteUploadthingFileUsingFileKey.useMutation({
      onSuccess: () => {
        toast.success("Profile image deleted successfully");
        form.setValue("imageUrl", "");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });

  const onSubmit = (data: TestimonialFormValues) => {
    if (isEditMode && testimonialId) {
      updateTestimonial.mutate({ ...data, id: testimonialId });
    } else {
      createTestimonial.mutate(data);
    }
  };

  const handleCancel = () => {
    if (!isEditMode && !testimonialId && form.getValues("imageUrl")) {
      deleteProfileImage.mutate({ fileKey: form.getValues("imageUrl") });
    }
    closeSheet();
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter name" {...field} />
              </FormControl>
              <FormDescription>The name of the person.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="designation"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Designation</FormLabel>
              <FormControl>
                <Input placeholder="Enter designation" {...field} />
              </FormControl>
              <FormDescription>
                The designation or role of the person.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="location"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Location</FormLabel>
              <FormControl>
                <Input placeholder="Enter location" {...field} />
              </FormControl>
              <FormDescription>
                The location (e.g., city, country) of the person.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="imageUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Profile Image</FormLabel>
              <FormControl>
                {field.value ? (
                  <div className="relative">
                    <Image
                      src={field.value}
                      alt="testimonial person"
                      height={200}
                      width={200}
                      className="h-32 w-32 rounded-full object-cover"
                    />

                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="mt-2"
                      disabled={deleteProfileImage.isPending}
                      onClick={() =>
                        deleteProfileImage.mutate({ fileKey: field.value })
                      }
                    >
                      {deleteProfileImage.isPending ? (
                        <>
                          <Loader2 className="animate-spin" />
                          Removing Image
                        </>
                      ) : (
                        <>
                          <Trash />
                          Remove Image
                        </>
                      )}
                    </Button>
                  </div>
                ) : (
                  <UploadButton
                    endpoint="imageUploader"
                    appearance={{
                      button:
                        "bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                      allowedContent: "text-muted-foreground text-xs",
                      container: "flex flex-col gap-2 w-full",
                    }}
                    content={{
                      button: ({ isUploading }) => (
                        <span className="flex items-center gap-2">
                          {isUploading ? (
                            <Loader2 className="animate-spin" />
                          ) : (
                            <Upload className="size-4" />
                          )}
                          {isUploading
                            ? "Uploading..."
                            : "Upload Profile Image"}
                        </span>
                      ),
                      allowedContent: "JPEG, PNG or WebP (max 4MB)",
                    }}
                    onClientUploadComplete={(res) => {
                      const imageUrl = res[0]?.ufsUrl;
                      if (!imageUrl) {
                        toast.error("Image upload failed");
                        return;
                      }

                      field.onChange(imageUrl);
                      toast.success("Image uploaded successfully");
                    }}
                    onUploadError={(error: Error) => {
                      toast.error(`Upload error: ${error.message}`);
                    }}
                  />
                )}
              </FormControl>
              <FormDescription>
                Profile picture of the testimonial person.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Content</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter the testimonial content"
                  className="min-h-[120px]"
                  {...field}
                />
              </FormControl>
              <FormDescription>The actual testimonial text.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-end gap-2 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            disabled={
              createTestimonial.isPending || updateTestimonial.isPending
            }
          >
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              createTestimonial.isPending || updateTestimonial.isPending
            }
          >
            {isEditMode ? "Update Testimonial" : "Create Testimonial"}
          </Button>
        </div>
      </form>
    </Form>
  );
};
