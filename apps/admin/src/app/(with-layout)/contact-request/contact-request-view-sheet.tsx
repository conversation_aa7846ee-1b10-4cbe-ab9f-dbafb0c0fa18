"use client";

import { Skeleton } from "@acme/ui/components/ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableRow,
} from "@acme/ui/components/ui/table";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { contactRequestParamName } from "~/lib/constants";
import { api } from "~/trpc/react";

const ContactRequestViewSheet = () => {
  const {
    isOpen,
    closeSheet,
    paramValue: contactRequestId,
  } = useSheet(contactRequestParamName);

  const { data: contactRequest, isLoading } =
    api.contactRequest.getContactRequestById.useQuery(
      { id: contactRequestId! },
      { enabled: !!contactRequestId },
    );

  const formatEnum = (value: string | undefined | null) => {
    return value ? value.toLowerCase().replace(/_/g, " ") : "N/A";
  };

  const formatDate = (date: Date | string | undefined | null) => {
    if (!date) return "N/A";
    try {
      return new Date(date).toDateString();
    } catch {
      return "Invalid Date";
    }
  };

  const detailRows = [
    { label: "Name", value: contactRequest?.name },
    { label: "Email", value: contactRequest?.email },
    { label: "Phone", value: contactRequest?.phone },
    { label: "City", value: contactRequest?.city },
    { label: "State", value: contactRequest?.state },
    {
      label: "Preferred Communication",
      value: formatEnum(contactRequest?.preferredCommunication),
    },
    {
      label: "Preferred Date",
      value: formatDate(contactRequest?.preferredDate),
    },
    { label: "Preferred Time Slot", value: contactRequest?.preferredTimeSlot },
    {
      label: "Referral Source",
      value: formatEnum(contactRequest?.referralSource),
    },
    {
      label: "Other Referral Details",
      value: contactRequest?.otherReferralDetails,
    },
    { label: "Status", value: formatEnum(contactRequest?.status) },
    { label: "Submitted At", value: formatDate(contactRequest?.createdAt) },
  ];

  return (
    <GenericSheet
      isOpen={isOpen}
      onClose={closeSheet}
      title="Contact Request Details"
    >
      <div className="py-4">
        <Table>
          <TableBody>
            {isLoading
              ? Array(detailRows.length)
                  .fill(0)
                  .map((_, i) => (
                    <TableRow key={`skeleton-${i}`}>
                      <TableCell className="w-1/3 font-medium">
                        <Skeleton className="h-5 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-5 w-full" />
                      </TableCell>
                    </TableRow>
                  ))
              : detailRows.map((row, i) => (
                  <TableRow key={`row-${i}`}>
                    <TableCell className="w-1/3 text-left font-medium">
                      {row.label}
                    </TableCell>
                    <TableCell className="text-sm">
                      {row.value ?? "N/A"}
                    </TableCell>
                  </TableRow>
                ))}
          </TableBody>
        </Table>
      </div>
    </GenericSheet>
  );
};

export default ContactRequestViewSheet;
