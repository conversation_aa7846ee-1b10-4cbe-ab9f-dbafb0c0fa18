import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { contactRequestParamName } from "~/lib/constants";
import { api, HydrateClient } from "~/trpc/server";
import { ContactRequestDataTable } from "./contact-form-data-table"; // Corrected import name
import ContactRequestViewSheet from "./contact-request-view-sheet";

export const metadata = {
  title: "Contact Requests Management",
  description: "Manage contact requests submitted through your website.",
};

interface ContactRequestsPageProps {
  searchParams: Promise<{ [contactRequestParamName]?: string }>;
}

const ContactRequestsPage = async ({
  searchParams,
}: ContactRequestsPageProps) => {
  const contactRequestId = (await searchParams)[contactRequestParamName];
  if (contactRequestId) {
    await api.contactRequest.getContactRequestById.prefetch({
      id: contactRequestId,
    });
  }

  await api.contactRequest.getAllContactRequests.prefetch();

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Contact Requests Management
            </h1>
            <p className="text-muted-foreground">
              View and manage contact requests submitted via the website form.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <ContactRequestDataTable />
        </Suspense>

        <ContactRequestViewSheet />
      </div>
    </HydrateClient>
  );
};

export default ContactRequestsPage;
