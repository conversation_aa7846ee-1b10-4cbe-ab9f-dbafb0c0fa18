"use client";

import type {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import { useState } from "react";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  AlertCircle,
  Calendar,
  CheckCircle,
  ChevronDown,
  Clock,
  Eye,
  MessageCircle,
  MoreHorizontal,
  Trash,
} from "lucide-react";
import { toast } from "sonner";

import { contactRequest, contactStatus } from "@acme/db/schema";
import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuPortal,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import { Input } from "@acme/ui/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";

import AlertWrapper from "~/components/shared/alert-wrapper";
import SortableColumnHeader from "~/components/shared/sortable-column-header";
import { useSheet } from "~/hooks/use-sheet";
import { contactRequestParamName } from "~/lib/constants";
import { api } from "~/trpc/react";

// Defining the type from the schema
type ContactRequest = typeof contactRequest.$inferSelect;
// Using the enum values from the schema
type ContactStatus = (typeof contactStatus.enumValues)[number];

export const ContactRequestDataTable = () => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const { openSheet } = useSheet(contactRequestParamName);

  const { data: contactRequests = [], refetch } =
    api.contactRequest.getAllContactRequests.useQuery();

  const updateStatusMutation =
    api.contactRequest.updateContactRequestStatus.useMutation({
      onSuccess: () => {
        toast.success("Contact request status updated successfully");
        void refetch();
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });

  const deleteMutation = api.contactRequest.deleteContactRequest.useMutation({
    onSuccess: () => {
      toast.success("Contact request deleted successfully");
      void refetch();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const statusConfig: Record<
    ContactStatus,
    { bg: string; text: string; icon: React.ReactNode }
  > = {
    PENDING: {
      bg: "bg-yellow-100",
      text: "text-yellow-800",
      icon: <Clock className="h-3 w-3" />,
    },
    CONTACTED: {
      bg: "bg-blue-100",
      text: "text-blue-800",
      icon: <MessageCircle className="h-3 w-3" />,
    },
    SCHEDULED: {
      bg: "bg-purple-100",
      text: "text-purple-800",
      icon: <Calendar className="h-3 w-3" />,
    },
    COMPLETED: {
      bg: "bg-green-100",
      text: "text-green-800",
      icon: <CheckCircle className="h-3 w-3" />,
    },
    NO_RESPONSE: {
      bg: "bg-red-100",
      text: "text-red-800",
      icon: <AlertCircle className="h-3 w-3" />,
    },
  };

  const columns: ColumnDef<ContactRequest>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "name",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Name" />
      ),
      cell: ({ row }) => (
        <div className="font-medium">{row.getValue("name")}</div>
      ),
    },
    {
      accessorKey: "email",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Email" />
      ),
      cell: ({ row }) => <div>{row.getValue("email")}</div>,
    },
    {
      accessorKey: "phone",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Phone" />
      ),
      cell: ({ row }) => <div>{row.getValue("phone")}</div>,
    },
    {
      accessorKey: "status",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const status = row.original.status as ContactStatus;
        // Now we guarantee 'status' will be in statusConfig
        const config = statusConfig[status];

        return (
          <div className="flex min-w-[120px] items-center">
            <span
              className={`flex items-center rounded-full px-2 py-1 text-xs font-medium capitalize ${config.bg} ${config.text}`}
            >
              <span className="mr-1">{config.icon}</span>
              {status.toLowerCase().replace(/_/g, " ")}
            </span>
          </div>
        );
      },
      filterFn: (row, id, value: string[]) => {
        const status = row.getValue(id);
        return value.includes(status as ContactStatus);
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <SortableColumnHeader column={column} title="Submitted At" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return <div>{date.toDateString()}</div>;
      },
    },
    {
      id: "actions",
      enableHiding: false,
      cell: ({ row }) => {
        const contactRequest = row.original;

        return (
          <>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuItem onClick={() => openSheet(contactRequest.id)}>
                  <Eye className="mr-2 h-4 w-4" /> View Details
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuSub>
                  <DropdownMenuSubTrigger>Update Status</DropdownMenuSubTrigger>
                  <DropdownMenuPortal>
                    <DropdownMenuSubContent>
                      {(contactStatus.enumValues as ContactStatus[]).map(
                        (status) => (
                          <DropdownMenuItem
                            key={status}
                            onClick={() =>
                              updateStatusMutation.mutate({
                                id: contactRequest.id,
                                status,
                              })
                            }
                            disabled={contactRequest.status === status}
                          >
                            <span className="mr-2">
                              {statusConfig[status].icon}
                            </span>
                            {status.toLowerCase().replace(/_/g, " ")}
                          </DropdownMenuItem>
                        ),
                      )}
                    </DropdownMenuSubContent>
                  </DropdownMenuPortal>
                </DropdownMenuSub>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  className="text-red-600"
                  onClick={(e) => e.stopPropagation()}
                >
                  <AlertWrapper
                    trigger={
                      <button
                        className="flex w-full items-center gap-2 text-left"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <Trash className="size-4" />
                        Delete
                      </button>
                    }
                    onConfirm={() =>
                      deleteMutation.mutate({ id: contactRequest.id })
                    }
                    title="Are you sure you want to delete this contact request?"
                    description="This action cannot be undone."
                  />
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </>
        );
      },
    },
  ];

  const table = useReactTable({
    data: contactRequests,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
    },
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <Input
          placeholder="Filter by name..."
          value={table.getColumn("name")?.getFilterValue() as string}
          onChange={(event) =>
            table.getColumn("name")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        {/* Add Status Filter Dropdown if needed */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline">
              Columns <ChevronDown className="ml-2 h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {table
              .getAllColumns()
              .filter((column) => column.getCanHide())
              .map((column) => {
                return (
                  <DropdownMenuCheckboxItem
                    key={column.id}
                    className="capitalize"
                    checked={column.getIsVisible()}
                    onCheckedChange={(value) =>
                      column.toggleVisibility(!!value)
                    }
                  >
                    {column.id}
                  </DropdownMenuCheckboxItem>
                );
              })}
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
      {/* ... rest of the table rendering ... */}
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ContactRequestDataTable;
