"use client";

import React from "react";
import { PlusIcon } from "lucide-react";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { blogParamName } from "~/lib/constants";
import { BlogCreateUpdateForm } from "./blog-create-update-form";

const BlogFormSheet = () => {
  const { isOpen, paramValue, openSheet, closeSheet } = useSheet(blogParamName);

  return (
    <div>
      <Button onClick={() => openSheet()}>
        <PlusIcon className="size-4" />
        Create Blog
      </Button>
      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title={paramValue ? "Edit Blog" : "Create Blog"}
        className="md:min-w-[700px] lg:min-w-[900px] xl:min-w-[1000px] 2xl:min-w-[1600px]"
      >
        <BlogCreateUpdateForm />
      </GenericSheet>
    </div>
  );
};

export default BlogFormSheet;
