"use client";

import type {
  Column,
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
} from "@tanstack/react-table";
import type { CSSProperties } from "react";
import { useState } from "react";
import {
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import {
  ChevronDown,
  Edit,
  Eye,
  EyeOff,
  MoreHorizontal,
  Trash,
} from "lucide-react";
import { toast } from "sonner";

import { Badge } from "@acme/ui/components/ui/badge";
import { Button } from "@acme/ui/components/ui/button";
import { Checkbox } from "@acme/ui/components/ui/checkbox";
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@acme/ui/components/ui/dropdown-menu";
import { Input } from "@acme/ui/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@acme/ui/components/ui/table";

import AlertWrapper from "~/components/shared/alert-wrapper";
import ImageDialog from "~/components/shared/image-dialog";
import SortableColumnHeader from "~/components/shared/sortable-column-header";
import { useSheet } from "~/hooks/use-sheet";
import { blogParamName } from "~/lib/constants";
import { api } from "~/trpc/react";
import BlogFormSheet from "./blog-form-sheet";

// Helper function for pinned column styling
const getPinnedStyles = <T,>(column: Column<T>): CSSProperties => {
  const isPinned = column.getIsPinned();

  return {
    position: isPinned ? "sticky" : "relative",
    right: isPinned === "right" ? 0 : undefined,
    background: isPinned ? "white" : undefined,
    zIndex: isPinned ? 1 : 0,
    opacity: isPinned ? 0.8 : 1,
  };
};

export const BlogDataTable = () => {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [rowSelection, setRowSelection] = useState({});
  const { openSheet } = useSheet(blogParamName);

  const { data: blogs = [], refetch } = api.blog.getAllBlogs.useQuery();
  const deleteBlog = api.blog.deleteBlog.useMutation({
    onSuccess: () => {
      toast.success("Blog deleted successfully");
      void refetch();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const togglePublishStatus = api.blog.togglePublishStatus.useMutation({
    onSuccess: (_, variables) => {
      toast.success(
        `Blog ${variables.published ? "published" : "unpublished"} successfully`,
      );
      void refetch();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const columns: ColumnDef<(typeof blogs)[number]>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "imageUrl",
      header: "Image",
      cell: ({ row }) => <ImageDialog imageUrl={row.original.imageUrl} />,
    },
    {
      accessorKey: "title",
      header: ({ column }) => {
        return <SortableColumnHeader column={column} title="Title" />;
      },
      cell: ({ row }) => {
        return (
          <div className="flex items-center space-x-3">
            <div className="max-w-[400px] truncate font-medium">
              {row.getValue("title")}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "excerpt",
      header: ({ column }) => {
        return <SortableColumnHeader column={column} title="Excerpt" />;
      },
      cell: ({ row }) => (
        <div className="max-w-[300px] truncate">{row.getValue("excerpt")}</div>
      ),
    },
    {
      accessorKey: "authorName",
      header: ({ column }) => {
        return <SortableColumnHeader column={column} title="Author" />;
      },
      cell: ({ row }) => <div>{row.getValue("authorName")}</div>,
    },
    {
      accessorKey: "tags",
      header: ({ column }) => {
        return <SortableColumnHeader column={column} title="Tags" />;
      },
      cell: ({ row }) => (
        <div className="flex flex-wrap gap-1">
          {row.original.tags?.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
          {row.original.tags?.length && row.original.tags.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{row.original.tags.length - 3}
            </Badge>
          )}
        </div>
      ),
    },
    {
      accessorKey: "published",
      header: ({ column }) => {
        return <SortableColumnHeader column={column} title="Status" />;
      },
      cell: ({ row }) => (
        <Badge
          variant={row.original.published ? "default" : "outline"}
          className={row.original.published ? "bg-green-500" : ""}
        >
          {row.original.published ? "Published" : "Draft"}
        </Badge>
      ),
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => {
        return <SortableColumnHeader column={column} title="Created At" />;
      },
      cell: ({ row }) => {
        const date = new Date(row.original.createdAt);
        return <div>{date.toDateString()}</div>;
      },
    },
    {
      id: "actions",
      enableHiding: false,
      enablePinning: true, // Enable pinning for this column
      cell: ({ row }) => {
        const blog = row.original;

        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="h-8 w-8 p-0 text-green-500">
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="size-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" sticky="always">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => openSheet(blog.id)}>
                <Edit className="size-4" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  togglePublishStatus.mutate({
                    id: blog.id,
                    published: !blog.published,
                  })
                }
              >
                {blog.published ? (
                  <>
                    <EyeOff className="h-4 w-4" />
                    Unpublish
                  </>
                ) : (
                  <>
                    <Eye className="h-4 w-4" />
                    Publish
                  </>
                )}
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="text-red-600"
                onClick={(e) => e.stopPropagation()}
              >
                <AlertWrapper
                  trigger={
                    <button
                      className="flex items-center gap-2"
                      onClick={(e) => e.stopPropagation()}
                    >
                      <Trash className="size-4" /> Delete
                    </button>
                  }
                  onConfirm={() => deleteBlog.mutate({ id: blog.id })}
                  title="Are you sure you want to delete this blog?"
                  description="This action cannot be undone."
                />
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  const table = useReactTable({
    data: blogs,
    columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      columnPinning: {
        right: ["actions"], // Pin the actions column to the right
      },
    },
    enableColumnPinning: true, // Enable column pinning feature
  });

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <Input
          placeholder="Filter blogs..."
          value={table.getColumn("title")?.getFilterValue() as string}
          onChange={(event) =>
            table.getColumn("title")?.setFilterValue(event.target.value)
          }
          className="max-w-sm"
        />
        <div className="flex gap-2">
          <BlogFormSheet />
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columns <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  );
                })}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      <div className="overflow-x-auto rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead
                      key={header.id}
                      style={getPinnedStyles(header.column)}
                    >
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                            header.column.columnDef.header,
                            header.getContext(),
                          )}
                    </TableHead>
                  );
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell
                      key={cell.id}
                      style={getPinnedStyles(cell.column)}
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-end space-x-2 py-4">
        <div className="flex-1 text-sm text-muted-foreground">
          {table.getFilteredSelectedRowModel().rows.length} of{" "}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
        <div className="space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            Next
          </Button>
        </div>
      </div>
    </div>
  );
};
