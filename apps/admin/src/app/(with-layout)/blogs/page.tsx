import { Suspense } from "react";

import { Separator } from "@acme/ui/components/ui/separator";

import DataTableSkeleton from "~/components/skeleton/data-table-skeleton";
import { blogParamName } from "~/lib/constants";
import { api, HydrateClient } from "~/trpc/server";
import { BlogDataTable } from "./blog-data-table";

export const metadata = {
  title: "Blogs Management",
  description: "Manage your website's blog posts",
};

interface BlogsPageProps {
  searchParams: Promise<{ [blogParamName]?: string }>;
}

const BlogsPage = async ({ searchParams }: BlogsPageProps) => {
  const blogId = (await searchParams)[blogParamName];
  if (blogId) {
    await api.blog.getBlogById.prefetch({ id: blogId });
  }

  await api.blog.getAllBlogs.prefetch();

  return (
    <HydrateClient>
      <div>
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Blogs Management
            </h1>
            <p className="text-muted-foreground">
              Create, edit and manage blog posts for your website.
            </p>
          </div>
        </div>
        <Separator className="my-6" />
        <Suspense fallback={<DataTableSkeleton />}>
          <BlogDataTable />
        </Suspense>
      </div>
    </HydrateClient>
  );
};

export default BlogsPage;
