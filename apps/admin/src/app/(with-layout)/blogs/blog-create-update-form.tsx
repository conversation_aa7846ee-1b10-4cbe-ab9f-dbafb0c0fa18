"use client";

import type { z } from "zod";
import { useState } from "react";
import dynamic from "next/dynamic";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { zodResolver } from "@hookform/resolvers/zod";
import { skipToken } from "@tanstack/react-query";
import {
  generateUploadButton,
  generateUploadDropzone,
} from "@uploadthing/react";
import { Loader2, Trash, Upload, X } from "lucide-react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { Button } from "@acme/ui/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@acme/ui/components/ui/form";
import { Input } from "@acme/ui/components/ui/input";
import { Switch } from "@acme/ui/components/ui/switch";
import { Textarea } from "@acme/ui/components/ui/textarea";
import { BlogCRUDSchema } from "@acme/validators/admin";

import type { OurFileRouter } from "~/app/api/uploadthing/core";
import { useSheet } from "~/hooks/use-sheet";
import { blogParamName } from "~/lib/constants";
import { api } from "~/trpc/react";
import BlogPreview from "./blog-preview";

export const UploadButton = generateUploadButton<OurFileRouter>();
export const UploadDropzone = generateUploadDropzone<OurFileRouter>();

const BlogEditor = dynamic(() => import("@acme/lexical-editor/editor"), {
  ssr: false,
});

type BlogFormValues = z.infer<typeof BlogCRUDSchema>;

export const BlogCreateUpdateForm = () => {
  const { paramValue: blogId, closeSheet } = useSheet(blogParamName);
  const [tagInput, setTagInput] = useState("");

  const trpcUtils = api.useUtils();
  const router = useRouter();
  const isEditMode = !!blogId;

  const { data: blogData } = api.blog.getBlogById.useQuery(
    blogId ? { id: blogId } : skipToken,
  );

  const form = useForm<BlogFormValues>({
    resolver: zodResolver(BlogCRUDSchema),
    defaultValues: blogData
      ? {
          title: blogData.title,
          excerpt: blogData.excerpt,
          content: blogData.content,
          imageUrl: blogData.imageUrl,
          authorName: blogData.authorName,
          tags: blogData.tags || [],
          published: blogData.published,
        }
      : {
          title: "",
          excerpt: "",
          content: "",
          imageUrl: "",
          authorName: "",
          tags: [],
          published: false,
        },
  });

  const createBlog = api.blog.createBlog.useMutation({
    onSuccess: () => {
      toast.success("Blog created successfully");
      void trpcUtils.blog.getAllBlogs.invalidate();
      closeSheet();
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const updateBlog = api.blog.updateBlog.useMutation({
    onSuccess: () => {
      toast.success("Blog updated successfully");
      void trpcUtils.blog.getAllBlogs.invalidate();
      router.push("/blogs");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const deleteBlogImage =
    api.utils.deleteUploadthingFileUsingFileKey.useMutation({
      onSuccess: () => {
        toast.success("Profile image deleted successfully");
        form.setValue("imageUrl", "");
      },
      onError: (error) => {
        toast.error(error.message);
      },
    });

  const handleAddTag = () => {
    if (tagInput.trim() !== "") {
      const currentTags = form.getValues("tags");
      if (!currentTags.includes(tagInput.trim())) {
        form.setValue("tags", [...currentTags, tagInput.trim()]);
      }
      setTagInput("");
    }
  };

  const handleRemoveTag = (tag: string) => {
    const currentTags = form.getValues("tags");
    form.setValue(
      "tags",
      currentTags.filter((t) => t !== tag),
    );
  };

  const onSubmit = (data: BlogFormValues) => {
    if (isEditMode) {
      updateBlog.mutate({ id: blogId, ...data });
    } else {
      createBlog.mutate(data);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          control={form.control}
          name="title"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Title</FormLabel>
              <FormControl>
                <Input placeholder="Enter blog title" {...field} />
              </FormControl>
              <FormDescription>The title of your blog post.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="excerpt"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Excerpt</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter a short excerpt for the blog"
                  className="resize-none"
                  {...field}
                />
              </FormControl>
              <FormDescription>
                A short summary of your blog post.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="imageUrl"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Image</FormLabel>
              <FormControl>
                {field.value ? (
                  <div className="relative">
                    <Image
                      src={field.value}
                      alt="testimonial person"
                      height={200}
                      width={200}
                      className="h-32 w-32 rounded-full object-cover"
                    />

                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      className="mt-2"
                      disabled={deleteBlogImage.isPending}
                      onClick={() =>
                        deleteBlogImage.mutate({ fileKey: field.value })
                      }
                    >
                      {deleteBlogImage.isPending ? (
                        <>
                          <Loader2 className="animate-spin" />
                          Removing Image
                        </>
                      ) : (
                        <>
                          <Trash />
                          Remove Image
                        </>
                      )}
                    </Button>
                  </div>
                ) : (
                  <UploadButton
                    endpoint="imageUploader"
                    appearance={{
                      button:
                        "bg-primary text-primary-foreground hover:bg-primary/90 rounded-md px-4 py-2 text-sm font-medium shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
                      allowedContent: "text-muted-foreground text-xs",
                      container: "flex flex-col gap-2 w-full",
                    }}
                    content={{
                      button: ({ isUploading }) => (
                        <span className="flex items-center gap-2">
                          {isUploading ? (
                            <Loader2 className="animate-spin" />
                          ) : (
                            <Upload className="size-4" />
                          )}
                          {isUploading
                            ? "Uploading..."
                            : "Upload Profile Image"}
                        </span>
                      ),
                      allowedContent: "JPEG, PNG or WebP (max 4MB)",
                    }}
                    onClientUploadComplete={(res) => {
                      const imageUrl = res[0]?.ufsUrl;
                      if (!imageUrl) {
                        toast.error("Image upload failed");
                        return;
                      }

                      field.onChange(imageUrl);
                      toast.success("Image uploaded successfully");
                    }}
                    onUploadError={(error: Error) => {
                      toast.error(`Upload error: ${error.message}`);
                    }}
                  />
                )}
              </FormControl>
              <FormDescription>
                Image for the blog post's featured image.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="authorName"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Author Name</FormLabel>
              <FormControl>
                <Input placeholder="Enter author name" {...field} />
              </FormControl>
              <FormDescription>Name of the blog post author.</FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div>
          <FormLabel>Tags</FormLabel>
          <div className="mt-2 flex items-center gap-2">
            <Input
              value={tagInput}
              onChange={(e) => setTagInput(e.target.value)}
              placeholder="Add a tag"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  handleAddTag();
                }
              }}
            />
            <Button type="button" variant="outline" onClick={handleAddTag}>
              Add
            </Button>
          </div>
          <div className="mt-2 flex flex-wrap gap-2">
            {form.watch("tags").map((tag) => (
              <div
                key={tag}
                className="flex items-center gap-1 rounded-md bg-muted px-2 py-1"
              >
                <span>{tag}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveTag(tag)}
                  className="text-xs hover:text-destructive"
                >
                  <X className="size-3" />
                </button>
              </div>
            ))}
          </div>
        </div>

        <FormField
          control={form.control}
          name="published"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
              <div className="space-y-0.5">
                <FormLabel className="text-base">Published</FormLabel>
                <FormDescription>
                  Make this blog post visible on your website.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="content"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="flex items-center justify-between">
                <div>Content</div>
                <BlogPreview content={form.watch("content")} />
              </FormLabel>
              <FormControl>
                <div className="min-h-[300px] w-full rounded-md border">
                  <BlogEditor
                    initialContent={field.value}
                    onChange={field.onChange}
                  />
                </div>
              </FormControl>
              <FormDescription>
                The main content of your blog post.
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-end gap-2">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/blogs")}
          >
            Cancel
          </Button>
          <Button type="submit">
            {isEditMode ? "Update Blog" : "Create Blog"}
          </Button>
        </div>
      </form>
    </Form>
  );
};
