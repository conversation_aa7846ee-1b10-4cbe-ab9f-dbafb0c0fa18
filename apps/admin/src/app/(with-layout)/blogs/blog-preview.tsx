import React from "react";
import dynamic from "next/dynamic";

import { But<PERSON> } from "@acme/ui/components/ui/button";

import { GenericSheet } from "~/components/shared/generic-sheet";
import { useSheet } from "~/hooks/use-sheet";
import { openBlogPreviewParamName } from "~/lib/constants";

const BlogPreviewRenderer = dynamic(
  () => import("@acme/lexical-editor/preview"),
  {
    ssr: false,
  },
);

const BlogPreview = ({ content }: { content: string }) => {
  const { isOpen, closeSheet, openSheet } = useSheet(openBlogPreviewParamName);

  return (
    <>
      <Button type="button" onClick={() => openSheet()}>
        Preview
      </Button>

      <GenericSheet
        isOpen={isOpen}
        onClose={closeSheet}
        title="Blog Preview"
        className="md:min-w-[700px] lg:min-w-[900px] xl:min-w-[1000px] 2xl:min-w-[1600px]"
      >
        <BlogPreviewRenderer content={content} />
      </GenericSheet>
    </>
  );
};

export default BlogPreview;
