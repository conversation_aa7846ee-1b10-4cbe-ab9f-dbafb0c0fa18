import "@acme/ui/globals.css";

import type { Metadata, Viewport } from "next";
import { Poppins } from "next/font/google";
import { headers } from "next/headers";
import { redirect } from "next/navigation";

import SidebarProviderWrapper from "~/components/shared/sidebar-provider";
import { auth } from "~/server/auth";
import { TRPCReactProvider } from "~/trpc/react";

export const metadata: Metadata = {
  title: "FluidPe Admin",
  description: "FluidPe admin pannel",
  openGraph: {
    title: "FluidPe",
    description: "FluidPe admin pannel",
  },
  icons: [{ rel: "icon", url: "/static/logos/day-logo.png" }],
};

export const viewport: Viewport = {
  themeColor: [
    { media: "(prefers-color-scheme: light)", color: "white" },
    { media: "(prefers-color-scheme: dark)", color: "black" },
  ],
};

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export default async function RootLayout(props: { children: React.ReactNode }) {
  const session = await auth.api.getSession({ headers: await headers() });

  //   remove this once middleware.ts is properly configured to manage the protected and public routes
  if (!session) {
    redirect("/auth/login");
  }

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${poppins.className} min-h-screen bg-green-600 font-sans text-foreground antialiased`}
      >
        <TRPCReactProvider>
          <SidebarProviderWrapper>{props.children}</SidebarProviderWrapper>
        </TRPCReactProvider>
      </body>
    </html>
  );
}
