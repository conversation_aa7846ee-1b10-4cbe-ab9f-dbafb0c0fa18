import "@acme/ui/globals.css";

import type { Metadata } from "next";
import { Poppins } from "next/font/google";

import { Toaster } from "@acme/ui/components/ui/sonner";

import { TRPCReactProvider } from "~/trpc/react";
import { HydrateClient } from "~/trpc/server";

export const metadata: Metadata = {
  title: "FluidPe Admin",
  description: "FluidPe admin pannel",
  openGraph: {
    title: "FluidPe",
    description: "FluidPe admin pannel",
  },
  icons: [{ rel: "icon", url: "/static/logos/day-logo.png" }],
};

const poppins = Poppins({
  subsets: ["latin"],
  variable: "--font-poppins",
  display: "swap",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html lang="en" className={`${poppins.className} font-open_sans`}>
      <body>
        <TRPCReactProvider>
          <HydrateClient>{children}</HydrateClient>
          <Toaster />
        </TRPCReactProvider>
      </body>
    </html>
  );
}
