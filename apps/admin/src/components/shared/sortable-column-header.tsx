import type { Column } from "@tanstack/react-table";
import { ArrowDown, ArrowUp } from "lucide-react";

import { Button } from "@acme/ui/components/ui/button";

const SortableColumnHeader = <T,>({
  column,
  title,
}: {
  column: Column<T, unknown>;
  title: string;
}) => {
  const isSorted = column.getIsSorted();

  return (
    <Button
      variant="ghost"
      size="sm"
      className="flex h-8 items-center gap-1 px-2 pl-0 font-medium hover:bg-muted/30"
      onClick={() => {
        const currentSort = column.getIsSorted();
        if (!currentSort) {
          column.toggleSorting(false);
        } else {
          column.toggleSorting(currentSort === "asc");
        }
      }}
    >
      {title}
      {isSorted ? (
        isSorted === "asc" ? (
          <ArrowUp className="ml-1 h-4 w-4 text-primary" />
        ) : (
          <ArrowDown className="ml-1 h-4 w-4 text-primary" />
        )
      ) : (
        <ArrowUp className="ml-1 h-4 w-4 opacity-30" />
      )}
    </Button>
  );
};

export default SortableColumnHeader;
