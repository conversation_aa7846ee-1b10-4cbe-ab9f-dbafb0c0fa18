import React from "react";
import Image from "next/image";

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  DialogTrigger,
} from "@acme/ui/components/ui/dialog";

interface ImageDialogProps {
  imageUrl: string;
}

const ImageDialog = ({ imageUrl }: ImageDialogProps) => {
  return (
    <Dialog>
      <DialogTrigger className="underline underline-offset-4">
        View Image
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <div className="relative aspect-video overflow-hidden">
            <Image
              src={imageUrl}
              alt="image"
              fill
              className="relative object-contain"
            />
          </div>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
};

export default ImageDialog;
