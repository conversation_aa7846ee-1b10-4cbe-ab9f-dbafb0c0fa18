"use client";

import React from "react";

import { Separator } from "@acme/ui/components/ui/separator";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@acme/ui/components/ui/sidebar";
import { Toaster } from "@acme/ui/components/ui/sonner";

import { AppSidebar } from "~/components/shared/app-sidebar";

const SidebarProviderWrapper = (props: { children: React.ReactNode }) => {
  return (
    <SidebarProvider className="">
      <AppSidebar />
      <SidebarInset className="overflow-x-auto">
        <header className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
          <SidebarTrigger className="-ml-1" />
          <Separator orientation="vertical" className="mr-2 h-4" />
        </header>
        <div className="flex w-full flex-1 flex-col gap-4 p-4">
          {props.children}
          <Toaster richColors />
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
};

export default SidebarProviderWrapper;
