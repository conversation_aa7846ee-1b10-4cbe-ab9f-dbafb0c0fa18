import React from "react";
import { Check, X } from "lucide-react";

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@acme/ui/components/ui/alert-dialog";
import { Button } from "@acme/ui/components/ui/button";

/**
 * AlertWrapper Props Interface
 * @interface AlertWrapperProps
 * @property {React.ReactNode} [trigger] - Custom trigger element. If not provided, a default button will be used.
 * @property {string} [title="Are you absolutely sure?"] - The title of the alert dialog.
 * @property {string} [description="This action cannot be undone."] - The description text of the alert dialog.
 * @property {string} [cancelText="Cancel"] - Text for the cancel button.
 * @property {string} [confirmText="Continue"] - Text for the confirm button.
 * @property {() => void} [onConfirm] - Callback function executed when the confirm button is clicked.
 * @property {() => void} [onCancel] - Callback function executed when the cancel button is clicked.
 * @property {boolean} [open] - Control the open state of the dialog externally.
 * @property {(open: boolean) => void} [onOpenChange] - Callback for when the open state changes.
 * @property {React.ReactNode} [children] - Children to render inside the dialog (alternative to description).
 * @property {"default" | "destructive" | "outline" | "secondary" | "ghost" | "link"} [confirmVariant="default"] - Variant for the confirm button.
 */
export interface AlertWrapperProps {
  trigger?: React.ReactNode;
  title?: string;
  description?: string;
  cancelText?: string;
  confirmText?: string;
  onConfirm?: () => void;
  onCancel?: () => void;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children?: React.ReactNode;
  confirmVariant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
}

/**
 * AlertWrapper Component
 *
 * A reusable alert dialog component that can be customized with various props.
 *
 * ## Why this component exists
 * This component simplifies the process of creating alert dialogs throughout the application by:
 * - Eliminating the need to repeatedly import multiple AlertDialog components
 * - Handling open/close state management internally when not controlled
 * - Providing convenient callback props for confirm/cancel actions
 * - Offering sensible defaults while allowing full customization
 * - Maintaining consistent styling and behavior across the application
 *
 * @example
 * // Basic usage
 * <AlertWrapper
 *   title="Delete item?"
 *   description="This will permanently delete this item."
 *   confirmText="Delete"
 *   confirmVariant="destructive"
 *   onConfirm={() => handleDelete()}
 * />
 *
 * @example
 * // With custom trigger
 * <AlertWrapper
 *   trigger={<Button variant="destructive">Delete</Button>}
 *   title="Confirm deletion"
 *   onConfirm={handleDelete}
 * />
 *
 * @example
 * // With controlled state
 * const [isOpen, setIsOpen] = useState(false);
 *
 * <AlertWrapper
 *   open={isOpen}
 *   onOpenChange={setIsOpen}
 *   title="Attention"
 *   description="Important notification"
 * />
 */

// Note: I would recommend to read above docs. Specifically the "Why this component exists" section.
const AlertWrapper: React.FC<AlertWrapperProps> = ({
  trigger,
  title = "Are you absolutely sure?",
  description = "This action cannot be undone.",
  cancelText = "Cancel",
  confirmText = "Continue",
  onConfirm,
  onCancel,
  open,
  onOpenChange,
  children,
  confirmVariant = "default",
}) => {
  const handleConfirm = () => {
    onConfirm?.();
  };

  const handleCancel = () => {
    onCancel?.();
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      <AlertDialogTrigger asChild>
        {trigger ?? <Button variant="outline">Show Dialog</Button>}
      </AlertDialogTrigger>
      <AlertDialogContent onClick={(e) => e.stopPropagation()}>
        <AlertDialogHeader>
          <AlertDialogTitle className="text-xl font-medium">
            {title}
          </AlertDialogTitle>
          {description && (
            <AlertDialogDescription>{description}</AlertDialogDescription>
          )}
        </AlertDialogHeader>
        {children}
        <AlertDialogFooter className="flex flex-row items-center justify-between">
          <AlertDialogCancel
            onClick={handleCancel}
            className="flex items-center gap-2"
          >
            <X className="size-4" />
            {cancelText}
          </AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              variant={confirmVariant}
              onClick={handleConfirm}
              className="flex items-center gap-2"
            >
              <Check className="size-4" />
              {confirmText}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default AlertWrapper;
