import { FileText, MessageCircleQuestion, Star, Users } from "lucide-react";

export const SIDEBAR_LINKS = {
  navMain: [
    {
      title: "FAQs",
      url: "/faqs",
      icon: MessageCircleQuestion,
      items: [],
    },
    {
      title: "Blogs",
      url: "/blogs",
      icon: FileText,
      items: [],
    },
    {
      title: "Testimonials",
      url: "/testimonials",
      icon: Star,
      items: [],
    },
    {
      title: "Contact Requests",
      url: "/contact-request",
      icon: Users,
      items: [],
    },
  ],
};

export const openBlogPreviewParamName = "blog_preview";

export const faqParamName = "faq_id";
export const blogParamName = "blog_id";
export const testimonialParamName = "testimonial_id";
export const contactRequestParamName = "contact_request_id";
